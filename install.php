<?php
/**
 * معالج التثبيت السريع لنظام إدارة شات بوت الواتساب
 * Quick Installation Handler for WhatsApp Chatbot Management System
 */

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/database.php')) {
    die('ملف الإعدادات غير موجود. يرجى التأكد من وجود config/database.php');
}

require_once 'config/database.php';

$step = $_GET['step'] ?? 1;
$errors = [];
$success_messages = [];

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($_POST['action']) {
        case 'test_database':
            try {
                $database = new Database();
                $db = $database->getConnection();
                if ($db) {
                    $success_messages[] = 'تم الاتصال بقاعدة البيانات بنجاح!';
                    $step = 2;
                } else {
                    $errors[] = 'فشل في الاتصال بقاعدة البيانات';
                }
            } catch (Exception $e) {
                $errors[] = 'خطأ في الاتصال: ' . $e->getMessage();
            }
            break;

        case 'create_tables':
            try {
                $database = new Database();
                $db = $database->getConnection();
                
                // قراءة ملف SQL
                $sql_content = file_get_contents('database.sql');
                if ($sql_content) {
                    // تقسيم الاستعلامات
                    $queries = explode(';', $sql_content);
                    $executed = 0;
                    
                    foreach ($queries as $query) {
                        $query = trim($query);
                        if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
                            try {
                                $db->exec($query);
                                $executed++;
                            } catch (PDOException $e) {
                                // تجاهل أخطاء الجداول الموجودة
                                if (strpos($e->getMessage(), 'already exists') === false) {
                                    throw $e;
                                }
                            }
                        }
                    }
                    
                    $success_messages[] = "تم إنشاء الجداول بنجاح! ($executed استعلام)";
                    $step = 3;
                } else {
                    $errors[] = 'لم يتم العثور على ملف database.sql';
                }
            } catch (Exception $e) {
                $errors[] = 'خطأ في إنشاء الجداول: ' . $e->getMessage();
            }
            break;

        case 'create_admin':
            try {
                $database = new Database();
                $db = $database->getConnection();
                
                $username = Helper::sanitizeInput($_POST['username']);
                $email = Helper::sanitizeInput($_POST['email']);
                $password = $_POST['password'];
                $full_name = Helper::sanitizeInput($_POST['full_name']);
                
                // التحقق من البيانات
                if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
                    throw new Exception('جميع الحقول مطلوبة');
                }
                
                if (!Helper::isValidEmail($email)) {
                    throw new Exception('البريد الإلكتروني غير صحيح');
                }
                
                if (strlen($password) < 8) {
                    throw new Exception('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                }
                
                // إنشاء المستخدم الإداري
                $password_hash = Helper::hashPassword($password);
                
                $query = "INSERT INTO users (username, email, password_hash, full_name, status) 
                         VALUES (:username, :email, :password_hash, :full_name, 'active')";
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':password_hash', $password_hash);
                $stmt->bindParam(':full_name', $full_name);
                
                if ($stmt->execute()) {
                    $success_messages[] = 'تم إنشاء حساب المدير بنجاح!';
                    $step = 4;
                } else {
                    throw new Exception('فشل في إنشاء حساب المدير');
                }
                
            } catch (Exception $e) {
                $errors[] = $e->getMessage();
            }
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة شات بوت الواتساب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .install-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 20px auto;
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .install-content {
            padding: 40px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -20px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        .step:last-child::after {
            display: none;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: bold;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        .requirements-list {
            list-style: none;
            padding: 0;
        }
        .requirements-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .requirements-list li:last-child {
            border-bottom: none;
        }
        .check-icon {
            color: #28a745;
            margin-left: 10px;
        }
        .error-icon {
            color: #dc3545;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1>
                    <i class="fab fa-whatsapp me-2"></i>
                    تثبيت نظام إدارة شات بوت الواتساب
                </h1>
                <p class="mb-0">مرحباً بك في معالج التثبيت السريع</p>
            </div>

            <div class="install-content">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                    <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
                </div>

                <!-- Messages -->
                <?php if (!empty($success_messages)): ?>
                    <?php foreach ($success_messages as $message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                    <?php foreach ($errors as $error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>

                <!-- Step Content -->
                <?php if ($step == 1): ?>
                    <!-- Step 1: System Requirements -->
                    <h3><i class="fas fa-server me-2"></i>التحقق من متطلبات النظام</h3>
                    <p>يرجى التأكد من توفر المتطلبات التالية:</p>
                    
                    <ul class="requirements-list">
                        <li>
                            <?php if (version_compare(PHP_VERSION, '7.4.0', '>=')): ?>
                                <i class="fas fa-check check-icon"></i>
                                PHP 7.4+ (الحالي: <?php echo PHP_VERSION; ?>)
                            <?php else: ?>
                                <i class="fas fa-times error-icon"></i>
                                PHP 7.4+ مطلوب (الحالي: <?php echo PHP_VERSION; ?>)
                            <?php endif; ?>
                        </li>
                        <li>
                            <?php if (extension_loaded('pdo_mysql')): ?>
                                <i class="fas fa-check check-icon"></i>
                                PDO MySQL Extension
                            <?php else: ?>
                                <i class="fas fa-times error-icon"></i>
                                PDO MySQL Extension غير متوفر
                            <?php endif; ?>
                        </li>
                        <li>
                            <?php if (extension_loaded('curl')): ?>
                                <i class="fas fa-check check-icon"></i>
                                cURL Extension
                            <?php else: ?>
                                <i class="fas fa-times error-icon"></i>
                                cURL Extension غير متوفر
                            <?php endif; ?>
                        </li>
                        <li>
                            <?php if (extension_loaded('json')): ?>
                                <i class="fas fa-check check-icon"></i>
                                JSON Extension
                            <?php else: ?>
                                <i class="fas fa-times error-icon"></i>
                                JSON Extension غير متوفر
                            <?php endif; ?>
                        </li>
                        <li>
                            <?php if (is_writable('.')): ?>
                                <i class="fas fa-check check-icon"></i>
                                صلاحيات الكتابة متوفرة
                            <?php else: ?>
                                <i class="fas fa-times error-icon"></i>
                                صلاحيات الكتابة غير متوفرة
                            <?php endif; ?>
                        </li>
                    </ul>

                    <form method="POST" action="">
                        <input type="hidden" name="action" value="test_database">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-2"></i>
                            اختبار الاتصال بقاعدة البيانات
                        </button>
                    </form>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Database Setup -->
                    <h3><i class="fas fa-database me-2"></i>إعداد قاعدة البيانات</h3>
                    <p>سيتم الآن إنشاء الجداول المطلوبة في قاعدة البيانات.</p>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تأكد من أن قاعدة البيانات <strong>whatsapp_bot_system</strong> موجودة ومُعدة بشكل صحيح.
                    </div>

                    <form method="POST" action="">
                        <input type="hidden" name="action" value="create_tables">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء الجداول
                        </button>
                    </form>

                <?php elseif ($step == 3): ?>
                    <!-- Step 3: Admin Account -->
                    <h3><i class="fas fa-user-shield me-2"></i>إنشاء حساب المدير</h3>
                    <p>أنشئ حساب المدير الأول للنظام:</p>

                    <form method="POST" action="">
                        <input type="hidden" name="action" value="create_admin">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="8">
                            <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إنشاء حساب المدير
                        </button>
                    </form>

                <?php elseif ($step == 4): ?>
                    <!-- Step 4: Installation Complete -->
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        <h3 class="mt-3 text-success">تم التثبيت بنجاح!</h3>
                        <p class="text-muted">النظام جاهز للاستخدام الآن</p>

                        <div class="alert alert-warning text-start mt-4">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>خطوات مهمة بعد التثبيت:</h5>
                            <ol>
                                <li>احذف ملف <code>install.php</code> لأسباب أمنية</li>
                                <li>قم بإعداد n8n webhook باستخدام الرابط:
                                    <br><code><?php echo Config::N8N_WEBHOOK_URL; ?></code>
                                </li>
                                <li>احصل على WhatsApp Business API credentials</li>
                                <li>قم بإعداد SSL certificate للموقع</li>
                                <li>راجع ملف README.md للمزيد من التفاصيل</li>
                            </ol>
                        </div>

                        <div class="d-flex gap-3 justify-content-center">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home me-2"></i>
                                الذهاب للموقع
                            </a>
                            <a href="login.php" class="btn btn-outline-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
