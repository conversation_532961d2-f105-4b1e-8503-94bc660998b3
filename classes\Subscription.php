<?php
require_once 'config/database.php';

/**
 * فئة إدارة الاشتراكات
 * Subscription Management Class
 */
class Subscription {
    private $conn;
    private $table_name = "user_subscriptions";
    private $plans_table = "subscription_plans";

    public $id;
    public $user_id;
    public $plan_id;
    public $start_date;
    public $end_date;
    public $status;
    public $payment_status;
    public $payment_method;
    public $transaction_id;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * إنشاء اشتراك جديد
     * Create new subscription
     */
    public function create() {
        // إلغاء الاشتراكات النشطة السابقة
        $this->deactivatePreviousSubscriptions();

        $query = "INSERT INTO " . $this->table_name . " 
                 (user_id, plan_id, start_date, end_date, status, payment_status, payment_method, transaction_id) 
                 VALUES (:user_id, :plan_id, :start_date, :end_date, :status, :payment_status, :payment_method, :transaction_id)";

        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':user_id', $this->user_id);
        $stmt->bindParam(':plan_id', $this->plan_id);
        $stmt->bindParam(':start_date', $this->start_date);
        $stmt->bindParam(':end_date', $this->end_date);
        $stmt->bindParam(':status', $this->status);
        $stmt->bindParam(':payment_status', $this->payment_status);
        $stmt->bindParam(':payment_method', $this->payment_method);
        $stmt->bindParam(':transaction_id', $this->transaction_id);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            Helper::logActivity($this->user_id, 'subscription_created', 'New subscription created for plan ID: ' . $this->plan_id);
            return true;
        }

        return false;
    }

    /**
     * إلغاء الاشتراكات النشطة السابقة
     * Deactivate previous active subscriptions
     */
    private function deactivatePreviousSubscriptions() {
        $query = "UPDATE " . $this->table_name . " 
                 SET status = 'cancelled' 
                 WHERE user_id = :user_id AND status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $this->user_id);
        $stmt->execute();
    }

    /**
     * الحصول على جميع خطط الاشتراك
     * Get all subscription plans
     */
    public function getAllPlans() {
        $query = "SELECT * FROM " . $this->plans_table . " WHERE is_active = 1 ORDER BY price ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * الحصول على خطة اشتراك محددة
     * Get specific subscription plan
     */
    public function getPlanById($plan_id) {
        $query = "SELECT * FROM " . $this->plans_table . " WHERE id = :plan_id AND is_active = 1 LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':plan_id', $plan_id);
        $stmt->execute();
        
        return $stmt->rowCount() > 0 ? $stmt->fetch() : null;
    }

    /**
     * الحصول على اشتراكات المستخدم
     * Get user subscriptions
     */
    public function getUserSubscriptions($user_id) {
        $query = "SELECT us.*, sp.name, sp.name_ar, sp.price, sp.duration_days, sp.max_messages_per_month, sp.max_contacts 
                 FROM " . $this->table_name . " us 
                 JOIN " . $this->plans_table . " sp ON us.plan_id = sp.id 
                 WHERE us.user_id = :user_id 
                 ORDER BY us.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * الحصول على الاشتراك النشط للمستخدم
     * Get user's active subscription
     */
    public function getActiveSubscription($user_id) {
        $query = "SELECT us.*, sp.name, sp.name_ar, sp.price, sp.max_messages_per_month, sp.max_contacts, sp.features 
                 FROM " . $this->table_name . " us 
                 JOIN " . $this->plans_table . " sp ON us.plan_id = sp.id 
                 WHERE us.user_id = :user_id 
                 AND us.status = 'active' 
                 AND us.end_date >= CURDATE() 
                 ORDER BY us.end_date DESC 
                 LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        return $stmt->rowCount() > 0 ? $stmt->fetch() : null;
    }

    /**
     * تحديث حالة الدفع
     * Update payment status
     */
    public function updatePaymentStatus($subscription_id, $payment_status, $transaction_id = null) {
        $query = "UPDATE " . $this->table_name . " 
                 SET payment_status = :payment_status";
        
        if ($transaction_id) {
            $query .= ", transaction_id = :transaction_id";
        }
        
        $query .= " WHERE id = :subscription_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':payment_status', $payment_status);
        $stmt->bindParam(':subscription_id', $subscription_id);
        
        if ($transaction_id) {
            $stmt->bindParam(':transaction_id', $transaction_id);
        }

        if ($stmt->execute()) {
            Helper::logActivity($this->user_id, 'payment_status_updated', 
                              'Payment status updated to: ' . $payment_status . ' for subscription: ' . $subscription_id);
            return true;
        }

        return false;
    }

    /**
     * التحقق من انتهاء الاشتراكات
     * Check for expired subscriptions
     */
    public function checkExpiredSubscriptions() {
        $query = "UPDATE " . $this->table_name . " 
                 SET status = 'expired' 
                 WHERE status = 'active' AND end_date < CURDATE()";
        
        $stmt = $this->conn->prepare($query);
        return $stmt->execute();
    }

    /**
     * حساب تاريخ انتهاء الاشتراك
     * Calculate subscription end date
     */
    public function calculateEndDate($start_date, $duration_days) {
        return date('Y-m-d', strtotime($start_date . ' + ' . $duration_days . ' days'));
    }

    /**
     * التحقق من إمكانية إرسال رسالة
     * Check if user can send message
     */
    public function canSendMessage($user_id) {
        $subscription = $this->getActiveSubscription($user_id);
        
        if (!$subscription) {
            return ['can_send' => false, 'reason' => 'لا يوجد اشتراك نشط'];
        }

        // التحقق من عدد الرسائل المرسلة هذا الشهر
        $current_month = date('Y-m');
        $query = "SELECT COUNT(*) as sent_count 
                 FROM message_usage 
                 WHERE user_id = :user_id 
                 AND message_type = 'sent' 
                 AND DATE_FORMAT(created_at, '%Y-%m') = :current_month";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':current_month', $current_month);
        $stmt->execute();
        
        $usage = $stmt->fetch();
        $sent_count = $usage['sent_count'];

        if ($sent_count >= $subscription['max_messages_per_month']) {
            return [
                'can_send' => false, 
                'reason' => 'تم الوصول للحد الأقصى من الرسائل لهذا الشهر',
                'limit' => $subscription['max_messages_per_month'],
                'used' => $sent_count
            ];
        }

        return [
            'can_send' => true,
            'remaining' => $subscription['max_messages_per_month'] - $sent_count,
            'limit' => $subscription['max_messages_per_month'],
            'used' => $sent_count
        ];
    }

    /**
     * الحصول على إحصائيات الاستخدام
     * Get usage statistics
     */
    public function getUsageStats($user_id, $period = 'current_month') {
        $where_clause = "WHERE user_id = :user_id";
        
        switch ($period) {
            case 'current_month':
                $where_clause .= " AND DATE_FORMAT(created_at, '%Y-%m') = '" . date('Y-m') . "'";
                break;
            case 'last_month':
                $where_clause .= " AND DATE_FORMAT(created_at, '%Y-%m') = '" . date('Y-m', strtotime('-1 month')) . "'";
                break;
            case 'last_7_days':
                $where_clause .= " AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'last_30_days':
                $where_clause .= " AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
        }

        $query = "SELECT 
                    COUNT(*) as total_messages,
                    SUM(CASE WHEN message_type = 'sent' THEN 1 ELSE 0 END) as sent_messages,
                    SUM(CASE WHEN message_type = 'received' THEN 1 ELSE 0 END) as received_messages,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_messages,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_messages,
                    SUM(cost) as total_cost,
                    COUNT(DISTINCT recipient_number) as unique_contacts
                 FROM message_usage 
                 " . $where_clause;

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * تجديد الاشتراك
     * Renew subscription
     */
    public function renewSubscription($subscription_id, $payment_method = null, $transaction_id = null) {
        // الحصول على بيانات الاشتراك الحالي
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :subscription_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':subscription_id', $subscription_id);
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            return false;
        }
        
        $current_subscription = $stmt->fetch();
        $plan = $this->getPlanById($current_subscription['plan_id']);
        
        if (!$plan) {
            return false;
        }

        // إنشاء اشتراك جديد
        $this->user_id = $current_subscription['user_id'];
        $this->plan_id = $current_subscription['plan_id'];
        $this->start_date = date('Y-m-d');
        $this->end_date = $this->calculateEndDate($this->start_date, $plan['duration_days']);
        $this->status = 'active';
        $this->payment_status = $payment_method ? 'paid' : 'pending';
        $this->payment_method = $payment_method;
        $this->transaction_id = $transaction_id;

        return $this->create();
    }
}
?>
