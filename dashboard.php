<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Subscription.php';
require_once 'classes/MessageManager.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?redirect=dashboard.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

$user = new User($db);
$user->getUserById($_SESSION['user_id']);

$subscription = new Subscription($db);
$active_subscription = $subscription->getActiveSubscription($_SESSION['user_id']);

$messageManager = new MessageManager($db);
$n8n_settings = $messageManager->getUserN8nSettings($_SESSION['user_id']);

// الحصول على الإحصائيات
$current_month_stats = $subscription->getUsageStats($_SESSION['user_id'], 'current_month');
$last_month_stats = $subscription->getUsageStats($_SESSION['user_id'], 'last_month');
$last_7_days_stats = $subscription->getUsageStats($_SESSION['user_id'], 'last_7_days');

// الحصول على الرسائل الأخيرة
$recent_messages = $messageManager->getUserMessages($_SESSION['user_id'], 10);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
        }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: all 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
        }
        .stat-card {
            text-align: center;
            padding: 30px 20px;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        .progress-custom {
            height: 12px;
            border-radius: 6px;
            background: #e9ecef;
        }
        .progress-bar {
            border-radius: 6px;
        }
        .table-custom {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .table-custom thead {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .badge-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
        }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .badge-info { background: #d1ecf1; color: #0c5460; }
        .quick-action-btn {
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: 600;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .welcome-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fab fa-whatsapp me-2"></i>
                <?php echo Config::SITE_NAME_AR; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-comments me-1"></i>
                            الرسائل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subscription.php">
                            <i class="fas fa-crown me-1"></i>
                            الاشتراك
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Header -->
        <div class="welcome-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="fas fa-user-circle me-2"></i>
                        مرحباً، <?php echo htmlspecialchars($user->full_name); ?>
                    </h2>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-calendar me-1"></i>
                        <?php echo date('l, d F Y'); ?>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <?php if ($active_subscription): ?>
                        <div class="badge bg-success fs-6 p-2">
                            <i class="fas fa-crown me-1"></i>
                            <?php echo htmlspecialchars($active_subscription['name_ar']); ?>
                        </div>
                    <?php else: ?>
                        <a href="subscription.php" class="btn btn-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            لا يوجد اشتراك نشط
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($active_subscription): ?>
            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="dashboard-card stat-card">
                        <i class="fas fa-paper-plane stat-icon text-primary"></i>
                        <div class="stat-number text-primary"><?php echo number_format($current_month_stats['sent_messages']); ?></div>
                        <div class="stat-label">رسائل مرسلة هذا الشهر</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="dashboard-card stat-card">
                        <i class="fas fa-inbox stat-icon text-success"></i>
                        <div class="stat-number text-success"><?php echo number_format($current_month_stats['received_messages']); ?></div>
                        <div class="stat-label">رسائل مستقبلة هذا الشهر</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="dashboard-card stat-card">
                        <i class="fas fa-users stat-icon text-info"></i>
                        <div class="stat-number text-info"><?php echo number_format($current_month_stats['unique_contacts']); ?></div>
                        <div class="stat-label">جهات اتصال فريدة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="dashboard-card stat-card">
                        <i class="fas fa-dollar-sign stat-icon text-warning"></i>
                        <div class="stat-number text-warning">$<?php echo number_format($current_month_stats['total_cost'], 2); ?></div>
                        <div class="stat-label">التكلفة هذا الشهر</div>
                    </div>
                </div>
            </div>

            <!-- Usage Progress -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="dashboard-card">
                        <h5 class="mb-4">
                            <i class="fas fa-chart-bar me-2 text-primary"></i>
                            استهلاك الرسائل الشهري
                        </h5>
                        
                        <?php 
                        $usage_percentage = ($current_month_stats['sent_messages'] / $active_subscription['max_messages_per_month']) * 100;
                        $remaining_messages = $active_subscription['max_messages_per_month'] - $current_month_stats['sent_messages'];
                        ?>
                        
                        <div class="row align-items-center mb-3">
                            <div class="col-md-8">
                                <div class="progress progress-custom">
                                    <div class="progress-bar bg-primary" role="progressbar" 
                                         style="width: <?php echo min($usage_percentage, 100); ?>%"
                                         aria-valuenow="<?php echo $usage_percentage; ?>" 
                                         aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <strong><?php echo number_format($usage_percentage, 1); ?>%</strong>
                            </div>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="text-muted small">المستخدم</div>
                                <div class="fw-bold"><?php echo number_format($current_month_stats['sent_messages']); ?></div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted small">المتبقي</div>
                                <div class="fw-bold text-success"><?php echo number_format($remaining_messages); ?></div>
                            </div>
                            <div class="col-4">
                                <div class="text-muted small">الإجمالي</div>
                                <div class="fw-bold"><?php echo number_format($active_subscription['max_messages_per_month']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="dashboard-card">
                        <h5 class="mb-4">
                            <i class="fas fa-bolt me-2 text-warning"></i>
                            إجراءات سريعة
                        </h5>
                        <div class="d-grid gap-2">
                            <a href="send-message.php" class="btn btn-primary quick-action-btn">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال رسالة
                            </a>
                            <a href="messages.php" class="btn btn-outline-primary quick-action-btn">
                                <i class="fas fa-comments me-2"></i>
                                عرض الرسائل
                            </a>
                            <a href="settings.php" class="btn btn-outline-secondary quick-action-btn">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات n8n
                            </a>
                            <a href="subscription.php" class="btn btn-outline-success quick-action-btn">
                                <i class="fas fa-crown me-2"></i>
                                إدارة الاشتراك
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Messages -->
            <?php if (!empty($recent_messages)): ?>
                <div class="dashboard-card">
                    <h5 class="mb-4">
                        <i class="fas fa-history me-2 text-info"></i>
                        الرسائل الأخيرة
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover table-custom">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>الرقم</th>
                                    <th>المحتوى</th>
                                    <th>الحالة</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_messages as $message): ?>
                                    <tr>
                                        <td>
                                            <?php if ($message['message_type'] == 'sent'): ?>
                                                <span class="badge badge-info">
                                                    <i class="fas fa-arrow-up me-1"></i>
                                                    مرسلة
                                                </span>
                                            <?php else: ?>
                                                <span class="badge badge-success">
                                                    <i class="fas fa-arrow-down me-1"></i>
                                                    مستقبلة
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($message['recipient_number']); ?></td>
                                        <td>
                                            <?php 
                                            $content = htmlspecialchars($message['message_content']);
                                            echo strlen($content) > 50 ? substr($content, 0, 50) . '...' : $content;
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_classes = [
                                                'success' => 'badge-success',
                                                'failed' => 'badge-danger',
                                                'pending' => 'badge-warning'
                                            ];
                                            $status_names = [
                                                'success' => 'نجح',
                                                'failed' => 'فشل',
                                                'pending' => 'في الانتظار'
                                            ];
                                            ?>
                                            <span class="badge-status <?php echo $status_classes[$message['status']] ?? 'badge-secondary'; ?>">
                                                <?php echo $status_names[$message['status']] ?? $message['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo Helper::formatDate($message['created_at'], 'd/m H:i'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="messages.php" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>
                            عرض جميع الرسائل
                        </a>
                    </div>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- No Active Subscription -->
            <div class="dashboard-card text-center py-5">
                <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-4 mb-3">لا يوجد اشتراك نشط</h3>
                <p class="text-muted mb-4">يرجى اختيار خطة اشتراك للبدء في استخدام الخدمة</p>
                <a href="subscription.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-shopping-cart me-2"></i>
                    اختر خطة الاشتراك
                </a>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>
