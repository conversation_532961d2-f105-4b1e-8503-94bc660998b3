{"name": "WhatsApp Bot Manager Integration", "nodes": [{"parameters": {"httpMethod": "POST", "path": "whatsapp-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-start", "name": "Webhook Start", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "a284c4ba-3c01-4fe7-9bce-0b18c3da3439"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "send_message"}]}}, "id": "check-action", "name": "Check Action", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "https://graph.facebook.com/v17.0/{{$json.phone_number_id}}/messages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$json.whatsapp_token}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "messaging_product", "value": "whatsapp"}, {"name": "to", "value": "={{$json.recipient}}"}, {"name": "type", "value": "text"}, {"name": "text", "value": "={\"body\": \"{{$json.message}}\"}"}]}, "options": {}}, "id": "send-whatsapp", "name": "Send WhatsApp Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [680, 200]}, {"parameters": {"url": "http://your-domain.com/ai1/api/webhook.php", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "action", "value": "update_message_status"}, {"name": "message_id", "value": "={{$json.message_id}}"}, {"name": "status", "value": "success"}, {"name": "execution_id", "value": "={{$workflow.id}}_{{$execution.id}}"}]}, "options": {}}, "id": "update-success", "name": "Update Success Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 200]}, {"parameters": {"url": "http://your-domain.com/ai1/api/webhook.php", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "action", "value": "update_message_status"}, {"name": "message_id", "value": "={{$json.message_id}}"}, {"name": "status", "value": "failed"}, {"name": "error", "value": "={{$json.error}}"}]}, "options": {}}, "id": "update-failed", "name": "Update Failed Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={\"success\": true, \"message\": \"Message sent successfully\", \"execution_id\": \"{{$workflow.id}}_{{$execution.id}}\"}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\"success\": false, \"message\": \"Failed to send message\", \"error\": \"{{$json.error}}\"}", "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "receive_message"}]}}, "id": "check-receive", "name": "Check Receive Message", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"url": "http://your-domain.com/ai1/api/webhook.php", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "action", "value": "receive_message"}, {"name": "user_id", "value": "={{$json.user_id}}"}, {"name": "sender", "value": "={{$json.sender}}"}, {"name": "message", "value": "={{$json.message}}"}, {"name": "execution_id", "value": "={{$workflow.id}}_{{$execution.id}}"}]}, "options": {}}, "id": "log-received", "name": "Log Received Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [680, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={\"success\": true, \"message\": \"Message received and logged\"}", "options": {}}, "id": "receive-response", "name": "Receive Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 500]}], "connections": {"Webhook Start": {"main": [[{"node": "Check Action", "type": "main", "index": 0}]]}, "Check Action": {"main": [[{"node": "Send WhatsApp Message", "type": "main", "index": 0}], [{"node": "Check Receive Message", "type": "main", "index": 0}]]}, "Send WhatsApp Message": {"main": [[{"node": "Update Success Status", "type": "main", "index": 0}]]}, "Update Success Status": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Update Failed Status": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}, "Check Receive Message": {"main": [[{"node": "Log Received Message", "type": "main", "index": 0}]]}, "Log Received Message": {"main": [[{"node": "Receive Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1", "id": "whatsapp-bot-manager", "meta": {"instanceId": "your-n8n-instance-id"}, "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "whatsapp", "name": "WhatsApp"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "chatbot", "name": "<PERSON><PERSON><PERSON>"}]}