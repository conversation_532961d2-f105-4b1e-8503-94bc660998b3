-- قاعدة بيانات نظام إدارة شات بوت الواتساب مع n8n
-- WhatsApp Chatbot Management System Database

CREATE DATABASE IF NOT EXISTS whatsapp_bot_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE whatsapp_bot_system;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    whatsapp_number VARCHAR(20),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدو<PERSON> خطط الاشتراك
CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    description_ar TEXT,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    duration_days INT NOT NULL,
    max_messages_per_month INT NOT NULL,
    max_contacts INT NOT NULL,
    features JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول اشتراكات المستخدمين
CREATE TABLE user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    payment_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id)
);

-- جدول استهلاك الرسائل
CREATE TABLE message_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    message_type ENUM('sent', 'received', 'template') NOT NULL,
    recipient_number VARCHAR(20),
    message_content TEXT,
    n8n_workflow_id VARCHAR(100),
    n8n_execution_id VARCHAR(100),
    status ENUM('success', 'failed', 'pending') DEFAULT 'pending',
    cost DECIMAL(8,4) DEFAULT 0.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id)
);

-- جدول إعدادات n8n للمستخدمين
CREATE TABLE user_n8n_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    n8n_webhook_url VARCHAR(500),
    n8n_api_key VARCHAR(255),
    whatsapp_business_api_token VARCHAR(500),
    whatsapp_phone_number_id VARCHAR(100),
    webhook_secret VARCHAR(100),
    is_configured BOOLEAN DEFAULT FALSE,
    last_sync TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول إحصائيات الاستخدام الشهرية
CREATE TABLE monthly_usage_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    year INT NOT NULL,
    month INT NOT NULL,
    messages_sent INT DEFAULT 0,
    messages_received INT DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_month (user_id, year, month)
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- إدراج خطط اشتراك افتراضية
INSERT INTO subscription_plans (name, name_ar, description, description_ar, price, duration_days, max_messages_per_month, max_contacts, features) VALUES
('Basic Plan', 'الخطة الأساسية', 'Perfect for small businesses', 'مثالية للشركات الصغيرة', 29.99, 30, 1000, 100, '["basic_support", "whatsapp_integration", "message_templates"]'),
('Pro Plan', 'الخطة المتقدمة', 'For growing businesses', 'للشركات النامية', 79.99, 30, 5000, 500, '["priority_support", "whatsapp_integration", "message_templates", "analytics", "custom_workflows"]'),
('Enterprise', 'خطة المؤسسات', 'For large organizations', 'للمؤسسات الكبيرة', 199.99, 30, 20000, 2000, '["24_7_support", "whatsapp_integration", "message_templates", "analytics", "custom_workflows", "api_access", "white_label"]');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_subscriptions_user_status ON user_subscriptions(user_id, status);
CREATE INDEX idx_subscriptions_dates ON user_subscriptions(start_date, end_date);
CREATE INDEX idx_message_usage_user_date ON message_usage(user_id, created_at);
CREATE INDEX idx_message_usage_subscription ON message_usage(subscription_id);
CREATE INDEX idx_monthly_stats_user_date ON monthly_usage_stats(user_id, year, month);
CREATE INDEX idx_activity_logs_user_date ON activity_logs(user_id, created_at);
