<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Subscription.php';

// التحقق من تسجيل الدخول
$is_logged_in = isset($_SESSION['user_id']);
$user = null;
$active_subscription = null;

if ($is_logged_in) {
    $database = new Database();
    $db = $database->getConnection();
    
    $user_obj = new User($db);
    if ($user_obj->getUserById($_SESSION['user_id'])) {
        $user = $user_obj;
        
        $subscription_obj = new Subscription($db);
        $active_subscription = $subscription_obj->getActiveSubscription($_SESSION['user_id']);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - الصفحة الرئيسية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .hero-section {
            padding: 100px 0;
            color: white;
            text-align: center;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .progress-custom {
            height: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fab fa-whatsapp me-2"></i>
                <?php echo Config::SITE_NAME_AR; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <?php if ($is_logged_in): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">
                                <i class="fas fa-comments me-1"></i>
                                الرسائل
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-1"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i>
                                إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <?php if ($is_logged_in): ?>
        <!-- Dashboard for logged in users -->
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <h2 class="text-white mb-4">
                        <i class="fas fa-user-circle me-2"></i>
                        مرحباً، <?php echo htmlspecialchars($user->full_name); ?>
                    </h2>
                </div>
            </div>

            <?php if ($active_subscription): ?>
                <div class="row">
                    <!-- Subscription Info -->
                    <div class="col-md-4">
                        <div class="dashboard-card">
                            <h5><i class="fas fa-crown me-2 text-warning"></i>خطة الاشتراك</h5>
                            <h4 class="text-primary"><?php echo htmlspecialchars($active_subscription['name_ar']); ?></h4>
                            <p class="text-muted">
                                ينتهي في: <?php echo Helper::formatDate($active_subscription['end_date'], 'd/m/Y'); ?>
                            </p>
                            <div class="mt-3">
                                <small class="text-muted">الحالة:</small>
                                <span class="badge bg-success">نشط</span>
                            </div>
                        </div>
                    </div>

                    <!-- Usage Stats -->
                    <div class="col-md-4">
                        <div class="dashboard-card">
                            <?php
                            $usage = $user->getCurrentMonthUsage();
                            $usage_percentage = ($usage['total_messages'] / $active_subscription['max_messages_per_month']) * 100;
                            ?>
                            <h5><i class="fas fa-chart-bar me-2 text-info"></i>الاستهلاك الشهري</h5>
                            <div class="stat-number"><?php echo number_format($usage['total_messages']); ?></div>
                            <p class="text-muted">من <?php echo number_format($active_subscription['max_messages_per_month']); ?> رسالة</p>
                            
                            <div class="progress progress-custom">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: <?php echo min($usage_percentage, 100); ?>%"
                                     aria-valuenow="<?php echo $usage_percentage; ?>" 
                                     aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <small class="text-muted">
                                <?php echo number_format($active_subscription['max_messages_per_month'] - $usage['total_messages']); ?> رسالة متبقية
                            </small>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="col-md-4">
                        <div class="dashboard-card">
                            <h5><i class="fas fa-bolt me-2 text-warning"></i>إجراءات سريعة</h5>
                            <div class="d-grid gap-2">
                                <a href="send-message.php" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    إرسال رسالة
                                </a>
                                <a href="settings.php" class="btn btn-outline-primary">
                                    <i class="fas fa-cog me-2"></i>
                                    إعدادات n8n
                                </a>
                                <a href="subscription.php" class="btn btn-outline-success">
                                    <i class="fas fa-upgrade me-2"></i>
                                    ترقية الخطة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- No active subscription -->
                <div class="row">
                    <div class="col-12">
                        <div class="dashboard-card text-center">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                            <h3 class="mt-3">لا يوجد اشتراك نشط</h3>
                            <p class="text-muted">يرجى اختيار خطة اشتراك للبدء في استخدام الخدمة</p>
                            <a href="subscription.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-shopping-cart me-2"></i>
                                اختر خطة الاشتراك
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

    <?php else: ?>
        <!-- Hero Section for visitors -->
        <div class="hero-section">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <h1 class="display-4 fw-bold mb-4">
                            <i class="fab fa-whatsapp me-3"></i>
                            مدير بوت الواتساب الذكي
                        </h1>
                        <p class="lead mb-5">
                            أنشئ وأدر شات بوت ذكي للواتساب باستخدام n8n. 
                            راقب الاستهلاك، أدر الاشتراكات، وتحكم في رسائلك بسهولة.
                        </p>
                        <div class="d-flex gap-3 justify-content-center">
                            <a href="register.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket me-2"></i>
                                ابدأ الآن مجاناً
                            </a>
                            <a href="login.php" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="container my-5">
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-robot feature-icon"></i>
                        <h4>شات بوت ذكي</h4>
                        <p>أنشئ شات بوت متقدم للواتساب باستخدام n8n مع إمكانيات الذكاء الاصطناعي</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-chart-line feature-icon"></i>
                        <h4>تتبع الاستهلاك</h4>
                        <p>راقب استهلاك الرسائل والتكاليف مع تقارير مفصلة وإحصائيات دقيقة</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <i class="fas fa-cogs feature-icon"></i>
                        <h4>تكامل سهل</h4>
                        <p>ربط سهل مع n8n وواجهة برمجة تطبيقات الواتساب للأعمال</p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
