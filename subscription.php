<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Subscription.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?redirect=subscription.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

$subscription = new Subscription($db);
$plans = $subscription->getAllPlans();
$user_subscriptions = $subscription->getUserSubscriptions($_SESSION['user_id']);
$active_subscription = $subscription->getActiveSubscription($_SESSION['user_id']);

$success_message = '';
$error_message = '';

// معالجة طلب الاشتراك
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['subscribe'])) {
    $plan_id = (int)$_POST['plan_id'];
    $payment_method = $_POST['payment_method'] ?? 'pending';
    
    $plan = $subscription->getPlanById($plan_id);
    if ($plan) {
        $subscription->user_id = $_SESSION['user_id'];
        $subscription->plan_id = $plan_id;
        $subscription->start_date = date('Y-m-d');
        $subscription->end_date = $subscription->calculateEndDate(date('Y-m-d'), $plan['duration_days']);
        $subscription->status = 'active';
        $subscription->payment_status = ($payment_method == 'free_trial') ? 'paid' : 'pending';
        $subscription->payment_method = $payment_method;
        $subscription->transaction_id = 'TXN_' . time() . '_' . $_SESSION['user_id'];
        
        if ($subscription->create()) {
            $success_message = 'تم الاشتراك بنجاح! يمكنك الآن البدء في استخدام الخدمة.';
            // إعادة تحميل البيانات
            $user_subscriptions = $subscription->getUserSubscriptions($_SESSION['user_id']);
            $active_subscription = $subscription->getActiveSubscription($_SESSION['user_id']);
        } else {
            $error_message = 'حدث خطأ أثناء إنشاء الاشتراك. يرجى المحاولة مرة أخرى.';
        }
    } else {
        $error_message = 'خطة الاشتراك غير موجودة.';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - خطط الاشتراك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .pricing-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            margin: 20px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        .pricing-card.featured {
            border: 3px solid #667eea;
            transform: scale(1.05);
        }
        .pricing-card.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 20px;
            right: -30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 40px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(45deg);
        }
        .price {
            font-size: 3rem;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }
        .price small {
            font-size: 1rem;
            color: #666;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 30px 0;
        }
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            color: #28a745;
            margin-left: 10px;
        }
        .btn-subscribe {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-subscribe:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: translateY(-2px);
            color: white;
        }
        .btn-current {
            background: #28a745;
            cursor: not-allowed;
        }
        .subscription-history {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
        .status-cancelled {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fab fa-whatsapp me-2"></i>
                <?php echo Config::SITE_NAME_AR; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="text-center text-white mb-5">
            <h1 class="display-4 fw-bold">
                <i class="fas fa-crown me-3"></i>
                خطط الاشتراك
            </h1>
            <p class="lead">اختر الخطة المناسبة لاحتياجاتك</p>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Current Subscription -->
        <?php if ($active_subscription): ?>
            <div class="subscription-history">
                <h4 class="mb-4">
                    <i class="fas fa-star text-warning me-2"></i>
                    اشتراكك الحالي
                </h4>
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="text-primary"><?php echo htmlspecialchars($active_subscription['name_ar']); ?></h5>
                        <p class="text-muted mb-2">
                            <i class="fas fa-calendar me-1"></i>
                            من <?php echo Helper::formatDate($active_subscription['start_date'], 'd/m/Y'); ?>
                            إلى <?php echo Helper::formatDate($active_subscription['end_date'], 'd/m/Y'); ?>
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-envelope me-1"></i>
                            <?php echo number_format($active_subscription['max_messages_per_month']); ?> رسالة شهرياً
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="status-badge status-active">نشط</span>
                        <div class="mt-2">
                            <strong class="text-success">${<?php echo number_format($active_subscription['price'], 2); ?></strong>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Pricing Plans -->
        <div class="row">
            <?php foreach ($plans as $index => $plan): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="pricing-card <?php echo $index == 1 ? 'featured' : ''; ?>">
                        <div class="text-center">
                            <h3 class="fw-bold"><?php echo htmlspecialchars($plan['name_ar']); ?></h3>
                            <p class="text-muted"><?php echo htmlspecialchars($plan['description_ar']); ?></p>
                            
                            <div class="price">
                                $<?php echo number_format($plan['price'], 2); ?>
                                <small>/شهر</small>
                            </div>

                            <ul class="feature-list">
                                <li>
                                    <i class="fas fa-check"></i>
                                    <?php echo number_format($plan['max_messages_per_month']); ?> رسالة شهرياً
                                </li>
                                <li>
                                    <i class="fas fa-check"></i>
                                    <?php echo number_format($plan['max_contacts']); ?> جهة اتصال
                                </li>
                                <?php 
                                $features = json_decode($plan['features'], true);
                                if ($features):
                                    foreach ($features as $feature):
                                        $feature_names = [
                                            'basic_support' => 'دعم فني أساسي',
                                            'priority_support' => 'دعم فني متقدم',
                                            '24_7_support' => 'دعم فني 24/7',
                                            'whatsapp_integration' => 'تكامل الواتساب',
                                            'message_templates' => 'قوالب الرسائل',
                                            'analytics' => 'التقارير والإحصائيات',
                                            'custom_workflows' => 'سير عمل مخصص',
                                            'api_access' => 'وصول API',
                                            'white_label' => 'علامة تجارية مخصصة'
                                        ];
                                ?>
                                    <li>
                                        <i class="fas fa-check"></i>
                                        <?php echo $feature_names[$feature] ?? $feature; ?>
                                    </li>
                                <?php 
                                    endforeach;
                                endif; 
                                ?>
                            </ul>

                            <?php if ($active_subscription && $active_subscription['plan_id'] == $plan['id']): ?>
                                <button class="btn btn-current" disabled>
                                    <i class="fas fa-check me-2"></i>
                                    الخطة الحالية
                                </button>
                            <?php else: ?>
                                <form method="POST" action="">
                                    <input type="hidden" name="plan_id" value="<?php echo $plan['id']; ?>">
                                    <input type="hidden" name="payment_method" value="pending">
                                    <button type="submit" name="subscribe" class="btn btn-subscribe">
                                        <i class="fas fa-shopping-cart me-2"></i>
                                        اشترك الآن
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Subscription History -->
        <?php if (!empty($user_subscriptions)): ?>
            <div class="subscription-history">
                <h4 class="mb-4">
                    <i class="fas fa-history me-2"></i>
                    تاريخ الاشتراكات
                </h4>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الخطة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>حالة الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_subscriptions as $sub): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($sub['name_ar']); ?></strong>
                                    </td>
                                    <td><?php echo Helper::formatDate($sub['start_date'], 'd/m/Y'); ?></td>
                                    <td><?php echo Helper::formatDate($sub['end_date'], 'd/m/Y'); ?></td>
                                    <td>$<?php echo number_format($sub['price'], 2); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $sub['status']; ?>">
                                            <?php 
                                            $status_names = [
                                                'active' => 'نشط',
                                                'expired' => 'منتهي',
                                                'cancelled' => 'ملغي'
                                            ];
                                            echo $status_names[$sub['status']] ?? $sub['status'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $payment_names = [
                                            'paid' => 'مدفوع',
                                            'pending' => 'في الانتظار',
                                            'failed' => 'فشل'
                                        ];
                                        echo $payment_names[$sub['payment_status']] ?? $sub['payment_status'];
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
