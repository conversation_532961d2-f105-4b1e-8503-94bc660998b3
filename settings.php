<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/MessageManager.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?redirect=settings.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

$messageManager = new MessageManager($db);
$n8n_settings = $messageManager->getUserN8nSettings($_SESSION['user_id']);

$success_message = '';
$error_message = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['save_n8n_settings'])) {
        $webhook_url = Helper::sanitizeInput($_POST['n8n_webhook_url']);
        $api_key = Helper::sanitizeInput($_POST['n8n_api_key']);
        $whatsapp_token = Helper::sanitizeInput($_POST['whatsapp_business_api_token']);
        $phone_number_id = Helper::sanitizeInput($_POST['whatsapp_phone_number_id']);
        $webhook_secret = Helper::sanitizeInput($_POST['webhook_secret']);

        if ($messageManager->updateN8nSettings($_SESSION['user_id'], $webhook_url, $api_key, $whatsapp_token, $phone_number_id, $webhook_secret)) {
            $success_message = 'تم حفظ إعدادات n8n بنجاح!';
            $n8n_settings = $messageManager->getUserN8nSettings($_SESSION['user_id']);
        } else {
            $error_message = 'حدث خطأ أثناء حفظ الإعدادات. يرجى المحاولة مرة أخرى.';
        }
    }
    
    // اختبار الاتصال مع n8n
    if (isset($_POST['test_connection'])) {
        $webhook_url = $_POST['n8n_webhook_url'];
        if (!empty($webhook_url)) {
            $test_data = [
                'action' => 'validate_webhook',
                'user_id' => $_SESSION['user_id'],
                'test_message' => 'اختبار الاتصال من النظام',
                'timestamp' => date('Y-m-d H:i:s')
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $webhook_url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'User-Agent: WhatsApp-Bot-Manager/1.0'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $error_message = 'خطأ في الاتصال: ' . $error;
            } elseif ($http_code >= 200 && $http_code < 300) {
                $success_message = 'تم الاتصال بـ n8n بنجاح! الكود: ' . $http_code;
            } else {
                $error_message = 'فشل الاتصال مع n8n. كود الخطأ: ' . $http_code;
            }
        } else {
            $error_message = 'يرجى إدخال رابط webhook أولاً';
        }
    }
}

$user = new User($db);
$user->getUserById($_SESSION['user_id']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - الإعدادات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .settings-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: bold;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        .btn-test {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        .btn-test:hover {
            background: linear-gradient(45deg, #20c997, #28a745);
            color: white;
        }
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-configured {
            background-color: #28a745;
        }
        .status-not-configured {
            background-color: #dc3545;
        }
        .webhook-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fab fa-whatsapp me-2"></i>
                <?php echo Config::SITE_NAME_AR; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-home me-1"></i>
                    الرئيسية
                </a>
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link active" href="settings.php">
                    <i class="fas fa-cog me-1"></i>
                    الإعدادات
                </a>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    تسجيل الخروج
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-cog me-2 text-primary"></i>
                    إعدادات النظام
                </h2>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <!-- n8n Settings -->
                <div class="settings-card">
                    <h4 class="mb-4">
                        <i class="fas fa-robot me-2 text-primary"></i>
                        إعدادات n8n
                        <span class="status-indicator <?php echo ($n8n_settings && $n8n_settings['is_configured']) ? 'status-configured' : 'status-not-configured'; ?>"></span>
                        <small class="text-muted">
                            (<?php echo ($n8n_settings && $n8n_settings['is_configured']) ? 'مُعد' : 'غير مُعد'; ?>)
                        </small>
                    </h4>

                    <form method="POST" action="">
                        <div class="mb-4">
                            <label for="n8n_webhook_url" class="form-label">
                                <i class="fas fa-link me-1"></i>
                                رابط n8n Webhook
                            </label>
                            <input type="url" class="form-control" id="n8n_webhook_url" name="n8n_webhook_url" 
                                   value="<?php echo htmlspecialchars($n8n_settings['n8n_webhook_url'] ?? 'https://n8n-n8n.mlqc5o.easypanel.host/webhook-test/a284c4ba-3c01-4fe7-9bce-0b18c3da3439'); ?>" 
                                   placeholder="https://your-n8n-instance.com/webhook/your-webhook-id" required>
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                الرابط الذي سيتم إرسال الرسائل إليه في n8n
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="n8n_api_key" class="form-label">
                                <i class="fas fa-key me-1"></i>
                                مفتاح API لـ n8n (اختياري)
                            </label>
                            <input type="password" class="form-control" id="n8n_api_key" name="n8n_api_key" 
                                   value="<?php echo htmlspecialchars($n8n_settings['n8n_api_key'] ?? ''); ?>" 
                                   placeholder="n8n_api_key_here">
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                مطلوب فقط إذا كان n8n يتطلب مصادقة API
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="whatsapp_business_api_token" class="form-label">
                                <i class="fab fa-whatsapp me-1"></i>
                                رمز WhatsApp Business API
                            </label>
                            <input type="password" class="form-control" id="whatsapp_business_api_token" name="whatsapp_business_api_token" 
                                   value="<?php echo htmlspecialchars($n8n_settings['whatsapp_business_api_token'] ?? ''); ?>" 
                                   placeholder="EAAxxxxxxxxxx" required>
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                احصل عليه من Facebook Developers Console
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="whatsapp_phone_number_id" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                معرف رقم الهاتف
                            </label>
                            <input type="text" class="form-control" id="whatsapp_phone_number_id" name="whatsapp_phone_number_id" 
                                   value="<?php echo htmlspecialchars($n8n_settings['whatsapp_phone_number_id'] ?? ''); ?>" 
                                   placeholder="123456789012345" required>
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Phone Number ID من WhatsApp Business API
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="webhook_secret" class="form-label">
                                <i class="fas fa-shield-alt me-1"></i>
                                مفتاح الأمان للـ Webhook (اختياري)
                            </label>
                            <input type="password" class="form-control" id="webhook_secret" name="webhook_secret" 
                                   value="<?php echo htmlspecialchars($n8n_settings['webhook_secret'] ?? ''); ?>" 
                                   placeholder="your_secret_key">
                            <div class="help-text">
                                <i class="fas fa-info-circle me-1"></i>
                                للتحقق من صحة الطلبات الواردة من n8n
                            </div>
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" name="save_n8n_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                            <button type="submit" name="test_connection" class="btn btn-test">
                                <i class="fas fa-plug me-2"></i>
                                اختبار الاتصال
                            </button>
                        </div>
                    </form>
                </div>

                <!-- User Profile Settings -->
                <div class="settings-card">
                    <h4 class="mb-4">
                        <i class="fas fa-user me-2 text-info"></i>
                        الملف الشخصي
                    </h4>

                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($user->full_name); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user->email); ?>" readonly>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($user->phone); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="whatsapp_number" class="form-label">رقم الواتساب</label>
                                <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                       value="<?php echo htmlspecialchars($user->whatsapp_number); ?>">
                            </div>
                        </div>

                        <button type="submit" name="update_profile" class="btn btn-primary">
                            <i class="fas fa-user-edit me-2"></i>
                            تحديث الملف الشخصي
                        </button>
                    </form>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Integration Guide -->
                <div class="settings-card">
                    <h5 class="mb-3">
                        <i class="fas fa-book me-2 text-success"></i>
                        دليل التكامل مع n8n
                    </h5>

                    <div class="webhook-info">
                        <h6><i class="fas fa-step-forward me-1"></i> خطوات الإعداد:</h6>
                        <ol class="small">
                            <li>أنشئ workflow جديد في n8n</li>
                            <li>أضف Webhook node كنقطة البداية</li>
                            <li>انسخ رابط الـ webhook وضعه أعلاه</li>
                            <li>أضف HTTP Request node للواتساب</li>
                            <li>احفظ الإعدادات واختبر الاتصال</li>
                        </ol>
                    </div>

                    <h6><i class="fas fa-code me-1"></i> مثال على البيانات المرسلة:</h6>
                    <div class="code-block">
{
  "action": "send_message",
  "user_id": <?php echo $_SESSION['user_id']; ?>,
  "recipient": "+966501234567",
  "message": "مرحباً!",
  "message_type": "text"
}
                    </div>

                    <div class="mt-3">
                        <h6><i class="fas fa-link me-1"></i> روابط مفيدة:</h6>
                        <ul class="list-unstyled small">
                            <li><a href="https://docs.n8n.io/" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt me-1"></i>
                                وثائق n8n
                            </a></li>
                            <li><a href="https://developers.facebook.com/docs/whatsapp" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt me-1"></i>
                                WhatsApp Business API
                            </a></li>
                            <li><a href="api/webhook.php" target="_blank" class="text-decoration-none">
                                <i class="fas fa-external-link-alt me-1"></i>
                                اختبار API
                            </a></li>
                        </ul>
                    </div>
                </div>

                <!-- System Status -->
                <div class="settings-card">
                    <h5 class="mb-3">
                        <i class="fas fa-heartbeat me-2 text-warning"></i>
                        حالة النظام
                    </h5>

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>n8n Webhook:</span>
                        <span class="badge <?php echo ($n8n_settings && !empty($n8n_settings['n8n_webhook_url'])) ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo ($n8n_settings && !empty($n8n_settings['n8n_webhook_url'])) ? 'مُعد' : 'غير مُعد'; ?>
                        </span>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>WhatsApp API:</span>
                        <span class="badge <?php echo ($n8n_settings && !empty($n8n_settings['whatsapp_business_api_token'])) ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo ($n8n_settings && !empty($n8n_settings['whatsapp_business_api_token'])) ? 'مُعد' : 'غير مُعد'; ?>
                        </span>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>رقم الهاتف:</span>
                        <span class="badge <?php echo ($n8n_settings && !empty($n8n_settings['whatsapp_phone_number_id'])) ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo ($n8n_settings && !empty($n8n_settings['whatsapp_phone_number_id'])) ? 'مُعد' : 'غير مُعد'; ?>
                        </span>
                    </div>

                    <?php if ($n8n_settings && $n8n_settings['last_sync']): ?>
                        <div class="mt-3 small text-muted">
                            <i class="fas fa-clock me-1"></i>
                            آخر مزامنة: <?php echo Helper::formatDate($n8n_settings['last_sync'], 'd/m/Y H:i'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // نسخ رقم الهاتف إلى رقم الواتساب
        document.getElementById('phone').addEventListener('input', function() {
            const whatsappField = document.getElementById('whatsapp_number');
            if (!whatsappField.value) {
                whatsappField.value = this.value;
            }
        });

        // إظهار/إخفاء كلمات المرور
        document.querySelectorAll('input[type="password"]').forEach(function(input) {
            const toggleBtn = document.createElement('button');
            toggleBtn.type = 'button';
            toggleBtn.className = 'btn btn-outline-secondary btn-sm';
            toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
            toggleBtn.style.position = 'absolute';
            toggleBtn.style.right = '10px';
            toggleBtn.style.top = '50%';
            toggleBtn.style.transform = 'translateY(-50%)';
            toggleBtn.style.zIndex = '10';
            
            input.parentNode.style.position = 'relative';
            input.style.paddingRight = '50px';
            input.parentNode.appendChild(toggleBtn);
            
            toggleBtn.addEventListener('click', function() {
                if (input.type === 'password') {
                    input.type = 'text';
                    toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    input.type = 'password';
                    toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });
    </script>
</body>
</html>
