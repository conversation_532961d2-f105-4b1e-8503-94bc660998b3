<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'whatsapp_bot_system';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            echo "Connection Error: " . $e->getMessage();
        }

        return $this->conn;
    }
}

/**
 * إعدادات عامة للنظام
 * General System Configuration
 */
class Config {
    // إعدادات الموقع
    const SITE_NAME = 'WhatsApp Bot Manager';
    const SITE_NAME_AR = 'مدير بوت الواتساب';
    const SITE_URL = 'http://localhost/ai1';
    const ADMIN_EMAIL = '<EMAIL>';
    
    // إعدادات الأمان
    const JWT_SECRET = 'your-secret-key-here-change-in-production';
    const PASSWORD_MIN_LENGTH = 8;
    const SESSION_TIMEOUT = 3600; // 1 hour
    
    // إعدادات n8n
    const N8N_BASE_URL = 'http://localhost:5678';
    const N8N_API_KEY = 'your-n8n-api-key';
    
    // إعدادات WhatsApp Business API
    const WHATSAPP_API_VERSION = 'v17.0';
    const WHATSAPP_VERIFY_TOKEN = 'your-verify-token';
    
    // إعدادات الدفع (يمكن إضافة PayPal, Stripe, إلخ)
    const PAYMENT_CURRENCY = 'USD';
    const PAYMENT_GATEWAY = 'stripe'; // stripe, paypal, local
    
    // إعدادات الرسائل
    const DEFAULT_MESSAGE_LIMIT = 1000;
    const DEFAULT_CONTACT_LIMIT = 100;
    
    // إعدادات التطبيق
    const TIMEZONE = 'Asia/Riyadh';
    const DATE_FORMAT = 'Y-m-d H:i:s';
    const LANGUAGE = 'ar'; // ar, en
    
    // مسارات الملفات
    const UPLOAD_PATH = 'uploads/';
    const LOG_PATH = 'logs/';
    const TEMP_PATH = 'temp/';
    
    public static function get($key, $default = null) {
        return defined('self::' . $key) ? constant('self::' . $key) : $default;
    }
}

/**
 * فئة المساعدات العامة
 * General Helper Functions
 */
class Helper {
    
    public static function sanitizeInput($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
    
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    public static function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }
    
    public static function isValidPhone($phone) {
        return preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $phone);
    }
    
    public static function formatDate($date, $format = null) {
        if (!$format) {
            $format = Config::DATE_FORMAT;
        }
        return date($format, strtotime($date));
    }
    
    public static function getCurrentMonth() {
        return date('Y-m');
    }
    
    public static function logActivity($user_id, $action, $description = '', $ip = null) {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            if (!$ip) {
                $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            }
            
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            $query = "INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) 
                     VALUES (:user_id, :action, :description, :ip_address, :user_agent)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':action', $action);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':ip_address', $ip);
            $stmt->bindParam(':user_agent', $user_agent);
            
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Error logging activity: " . $e->getMessage());
            return false;
        }
    }
    
    public static function sendJsonResponse($data, $status_code = 200) {
        http_response_code($status_code);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    public static function redirect($url) {
        header("Location: " . $url);
        exit;
    }
}

// تعيين المنطقة الزمنية
date_default_timezone_set(Config::TIMEZONE);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
