<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Subscription.php';
require_once 'classes/MessageManager.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php?redirect=send-message.php');
    exit;
}

$database = new Database();
$db = $database->getConnection();

$messageManager = new MessageManager($db);
$subscription = new Subscription($db);

// التحقق من الاشتراك النشط
$active_subscription = $subscription->getActiveSubscription($_SESSION['user_id']);
if (!$active_subscription) {
    header('Location: subscription.php');
    exit;
}

// التحقق من إمكانية الإرسال
$can_send = $subscription->canSendMessage($_SESSION['user_id']);

$success_message = '';
$error_message = '';

// معالجة إرسال الرسالة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['send_message'])) {
    $recipient = Helper::sanitizeInput($_POST['recipient']);
    $message = Helper::sanitizeInput($_POST['message']);
    $message_type = $_POST['message_type'] ?? 'text';

    // التحقق من صحة البيانات
    if (empty($recipient) || empty($message)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!Helper::isValidPhone($recipient)) {
        $error_message = 'رقم الهاتف غير صحيح';
    } elseif (!$can_send['can_send']) {
        $error_message = $can_send['reason'];
    } else {
        // إرسال الرسالة
        $result = $messageManager->sendMessage($_SESSION['user_id'], $recipient, $message, $message_type);
        
        if ($result['success']) {
            $success_message = 'تم إرسال الرسالة بنجاح! معرف الرسالة: ' . $result['message_id'];
            // إعادة تحميل حدود الإرسال
            $can_send = $subscription->canSendMessage($_SESSION['user_id']);
        } else {
            $error_message = 'فشل في إرسال الرسالة: ' . $result['error'];
        }
    }
}

// الحصول على الرسائل الأخيرة
$recent_messages = $messageManager->getUserMessages($_SESSION['user_id'], 5, 0, 'sent');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - إرسال رسالة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .message-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: bold;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
        }
        .btn-primary:disabled {
            background: #6c757d;
            opacity: 0.6;
        }
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .usage-info {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .usage-progress {
            background: rgba(255,255,255,0.2);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .usage-progress-bar {
            background: white;
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .message-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        .recent-messages {
            max-height: 400px;
            overflow-y: auto;
        }
        .message-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border-left: 3px solid #28a745;
        }
        .message-item.failed {
            border-left-color: #dc3545;
        }
        .message-item.pending {
            border-left-color: #ffc107;
        }
        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
            text-align: left;
        }
        .char-counter.warning {
            color: #ffc107;
        }
        .char-counter.danger {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fab fa-whatsapp me-2"></i>
                <?php echo Config::SITE_NAME_AR; ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    لوحة التحكم
                </a>
                <a class="nav-link active" href="send-message.php">
                    <i class="fas fa-paper-plane me-1"></i>
                    إرسال رسالة
                </a>
                <a class="nav-link" href="messages.php">
                    <i class="fas fa-comments me-1"></i>
                    الرسائل
                </a>
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cog me-1"></i>
                    الإعدادات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-paper-plane me-2 text-primary"></i>
                    إرسال رسالة جديدة
                </h2>
            </div>
        </div>

        <!-- Usage Information -->
        <div class="usage-info">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-2">
                        <i class="fas fa-chart-bar me-2"></i>
                        استهلاك الرسائل الشهري
                    </h5>
                    <div class="usage-progress">
                        <div class="usage-progress-bar" style="width: <?php echo min(($can_send['used'] / $can_send['limit']) * 100, 100); ?>%"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>المستخدم: <?php echo number_format($can_send['used']); ?></span>
                        <span>المتبقي: <?php echo number_format($can_send['remaining'] ?? 0); ?></span>
                        <span>الإجمالي: <?php echo number_format($can_send['limit']); ?></span>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="h3 mb-0"><?php echo number_format($can_send['remaining'] ?? 0); ?></div>
                    <small>رسالة متبقية</small>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <!-- Send Message Form -->
                <div class="message-card">
                    <h4 class="mb-4">
                        <i class="fas fa-edit me-2 text-success"></i>
                        كتابة رسالة جديدة
                    </h4>

                    <form method="POST" action="" id="messageForm">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="recipient" class="form-label">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم المستقبل
                                </label>
                                <input type="tel" class="form-control" id="recipient" name="recipient" 
                                       value="<?php echo htmlspecialchars($_POST['recipient'] ?? ''); ?>" 
                                       placeholder="+966501234567" required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يجب أن يبدأ الرقم برمز الدولة (مثل: +966)
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="message_type" class="form-label">
                                    <i class="fas fa-tag me-1"></i>
                                    نوع الرسالة
                                </label>
                                <select class="form-select" id="message_type" name="message_type">
                                    <option value="text" selected>نص</option>
                                    <option value="template">قالب</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                محتوى الرسالة
                            </label>
                            <textarea class="form-control" id="message" name="message" rows="6" 
                                      placeholder="اكتب رسالتك هنا..." required maxlength="4096"><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            <div class="d-flex justify-content-between">
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الحد الأقصى 4096 حرف
                                </div>
                                <div class="char-counter" id="charCounter">0 / 4096</div>
                            </div>
                        </div>

                        <!-- Message Preview -->
                        <div class="message-preview" id="messagePreview" style="display: none;">
                            <h6><i class="fas fa-eye me-1"></i> معاينة الرسالة:</h6>
                            <div id="previewContent"></div>
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" name="send_message" class="btn btn-primary" 
                                    <?php echo !$can_send['can_send'] ? 'disabled' : ''; ?>>
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال الرسالة
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                <i class="fas fa-eraser me-2"></i>
                                مسح النموذج
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="togglePreview()">
                                <i class="fas fa-eye me-2"></i>
                                معاينة
                            </button>
                        </div>

                        <?php if (!$can_send['can_send']): ?>
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($can_send['reason']); ?>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Quick Templates -->
                <div class="message-card">
                    <h5 class="mb-3">
                        <i class="fas fa-templates me-2 text-info"></i>
                        قوالب سريعة
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="insertTemplate('مرحباً! كيف يمكنني مساعدتك اليوم؟')">
                            <i class="fas fa-plus me-1"></i>
                            ترحيب
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="insertTemplate('شكراً لتواصلك معنا. سنقوم بالرد عليك في أقرب وقت ممكن.')">
                            <i class="fas fa-plus me-1"></i>
                            شكر
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="insertTemplate('عذراً، نحن مغلقون حالياً. ساعات العمل من 9 صباحاً إلى 6 مساءً.')">
                            <i class="fas fa-plus me-1"></i>
                            خارج ساعات العمل
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="insertTemplate('للمزيد من المعلومات، يرجى زيارة موقعنا الإلكتروني أو الاتصال بنا.')">
                            <i class="fas fa-plus me-1"></i>
                            معلومات إضافية
                        </button>
                    </div>
                </div>

                <!-- Recent Messages -->
                <?php if (!empty($recent_messages)): ?>
                    <div class="message-card">
                        <h5 class="mb-3">
                            <i class="fas fa-history me-2 text-warning"></i>
                            الرسائل الأخيرة
                        </h5>
                        
                        <div class="recent-messages">
                            <?php foreach ($recent_messages as $msg): ?>
                                <div class="message-item <?php echo $msg['status']; ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <strong><?php echo htmlspecialchars($msg['recipient_number']); ?></strong>
                                        <small class="text-muted"><?php echo Helper::formatDate($msg['created_at'], 'H:i'); ?></small>
                                    </div>
                                    <div class="small">
                                        <?php 
                                        $content = htmlspecialchars($msg['message_content']);
                                        echo strlen($content) > 60 ? substr($content, 0, 60) . '...' : $content;
                                        ?>
                                    </div>
                                    <div class="mt-1">
                                        <?php
                                        $status_classes = [
                                            'success' => 'success',
                                            'failed' => 'danger',
                                            'pending' => 'warning'
                                        ];
                                        $status_names = [
                                            'success' => 'تم الإرسال',
                                            'failed' => 'فشل',
                                            'pending' => 'في الانتظار'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $status_classes[$msg['status']] ?? 'secondary'; ?>">
                                            <?php echo $status_names[$msg['status']] ?? $msg['status']; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="messages.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>
                                عرض جميع الرسائل
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Tips -->
                <div class="message-card">
                    <h5 class="mb-3">
                        <i class="fas fa-lightbulb me-2 text-success"></i>
                        نصائح مفيدة
                    </h5>
                    
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            تأكد من صحة رقم الهاتف مع رمز الدولة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            استخدم رسائل قصيرة وواضحة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            تجنب الرسائل المتكررة للرقم نفسه
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-1"></i>
                            راقب حدود الاستهلاك الشهري
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عداد الأحرف
        const messageTextarea = document.getElementById('message');
        const charCounter = document.getElementById('charCounter');
        
        function updateCharCounter() {
            const length = messageTextarea.value.length;
            const maxLength = 4096;
            charCounter.textContent = `${length} / ${maxLength}`;
            
            if (length > maxLength * 0.9) {
                charCounter.className = 'char-counter danger';
            } else if (length > maxLength * 0.7) {
                charCounter.className = 'char-counter warning';
            } else {
                charCounter.className = 'char-counter';
            }
        }
        
        messageTextarea.addEventListener('input', updateCharCounter);
        updateCharCounter();

        // إدراج القوالب
        function insertTemplate(template) {
            const currentMessage = messageTextarea.value;
            if (currentMessage && !currentMessage.endsWith('\n')) {
                messageTextarea.value = currentMessage + '\n\n' + template;
            } else {
                messageTextarea.value = currentMessage + template;
            }
            updateCharCounter();
            messageTextarea.focus();
        }

        // مسح النموذج
        function clearForm() {
            if (confirm('هل أنت متأكد من مسح النموذج؟')) {
                document.getElementById('messageForm').reset();
                updateCharCounter();
                document.getElementById('messagePreview').style.display = 'none';
            }
        }

        // معاينة الرسالة
        function togglePreview() {
            const preview = document.getElementById('messagePreview');
            const previewContent = document.getElementById('previewContent');
            const message = messageTextarea.value;
            
            if (preview.style.display === 'none' || !preview.style.display) {
                if (message.trim()) {
                    previewContent.innerHTML = message.replace(/\n/g, '<br>');
                    preview.style.display = 'block';
                } else {
                    alert('يرجى كتابة رسالة أولاً');
                }
            } else {
                preview.style.display = 'none';
            }
        }

        // تنسيق رقم الهاتف
        const recipientInput = document.getElementById('recipient');
        recipientInput.addEventListener('input', function() {
            let value = this.value.replace(/[^\d+]/g, '');
            if (value && !value.startsWith('+')) {
                value = '+' + value;
            }
            this.value = value;
        });
    </script>
</body>
</html>
