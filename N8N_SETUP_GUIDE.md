# دليل إعداد n8n مع نظام إدارة شات بوت الواتساب

## 🚀 نظرة عامة

هذا الدليل يوضح كيفية ربط نظام إدارة شات بوت الواتساب مع n8n باستخدام الرابط المتوفر:
```
https://n8n-n8n.mlqc5o.easypanel.host/webhook-test/a284c4ba-3c01-4fe7-9bce-0b18c3da3439
```

## 📋 المتطلبات المسبقة

### 1. حساب WhatsApp Business API
- حساب Facebook Developer
- تطبيق WhatsApp Business API
- رمز الوصول (Access Token)
- معرف رقم الهاتف (Phone Number ID)

### 2. إعداد n8n
- الوصول إلى n8n instance الخاص بك
- صلاحيات إنشاء وتعديل workflows

## 🔧 خطوات الإعداد

### الخطوة 1: إعداد Workflow في n8n

1. **افتح n8n Dashboard:**
   ```
   https://n8n-n8n.mlqc5o.easypanel.host
   ```

2. **أنشئ Workflow جديد:**
   - اضغط على "New Workflow"
   - أعطه اسم: "WhatsApp Bot Manager Integration"

3. **أضف Webhook Node:**
   - اسحب "Webhook" node إلى canvas
   - اضبط الإعدادات:
     - HTTP Method: `POST`
     - Path: `whatsapp-webhook`
     - Response Mode: `Response Node`

### الخطوة 2: إعداد معالجة الرسائل

1. **أضف IF Node للتحقق من نوع العملية:**
   ```json
   {
     "conditions": {
       "string": [
         {
           "value1": "={{$json.action}}",
           "operation": "equal",
           "value2": "send_message"
         }
       ]
     }
   }
   ```

2. **أضف HTTP Request Node لإرسال رسائل WhatsApp:**
   ```json
   {
     "url": "https://graph.facebook.com/v17.0/{{$json.phone_number_id}}/messages",
     "method": "POST",
     "headers": {
       "Authorization": "Bearer {{$json.whatsapp_token}}",
       "Content-Type": "application/json"
     },
     "body": {
       "messaging_product": "whatsapp",
       "to": "={{$json.recipient}}",
       "type": "text",
       "text": {
         "body": "={{$json.message}}"
       }
     }
   }
   ```

3. **أضف HTTP Request Node لتحديث حالة الرسالة:**
   ```json
   {
     "url": "http://your-domain.com/ai1/api/webhook.php",
     "method": "POST",
     "headers": {
       "Content-Type": "application/json"
     },
     "body": {
       "action": "update_message_status",
       "message_id": "={{$json.message_id}}",
       "status": "success",
       "execution_id": "={{$workflow.id}}_{{$execution.id}}"
     }
   }
   ```

### الخطوة 3: إعداد معالجة الأخطاء

1. **أضف Error Handling:**
   - أضف "On Error" connection من WhatsApp HTTP Request
   - أضف HTTP Request Node لتحديث حالة الفشل

2. **إعداد Response Nodes:**
   - أضف "Respond to Webhook" nodes للنجاح والفشل

### الخطوة 4: حفظ وتفعيل Workflow

1. **احفظ الـ Workflow:**
   - اضغط Ctrl+S أو استخدم زر Save

2. **فعل الـ Workflow:**
   - اضغط على زر "Active" في أعلى اليمين

3. **انسخ رابط الـ Webhook:**
   ```
   https://n8n-n8n.mlqc5o.easypanel.host/webhook-test/a284c4ba-3c01-4fe7-9bce-0b18c3da3439
   ```

## 🔗 ربط النظام مع n8n

### في نظام إدارة شات بوت الواتساب:

1. **سجل دخول إلى النظام**
2. **اذهب إلى صفحة الإعدادات**
3. **أدخل البيانات التالية:**

```
رابط n8n Webhook:
https://n8n-n8n.mlqc5o.easypanel.host/webhook-test/a284c4ba-3c01-4fe7-9bce-0b18c3da3439

رمز WhatsApp Business API:
EAAxxxxxxxxxx (احصل عليه من Facebook Developers)

معرف رقم الهاتف:
123456789012345 (من WhatsApp Business API)
```

4. **اضغط "حفظ الإعدادات"**
5. **اضغط "اختبار الاتصال"**

## 📊 اختبار التكامل

### 1. اختبار إرسال رسالة:

```bash
curl -X POST https://n8n-n8n.mlqc5o.easypanel.host/webhook-test/a284c4ba-3c01-4fe7-9bce-0b18c3da3439 \
  -H "Content-Type: application/json" \
  -d '{
    "action": "send_message",
    "user_id": 1,
    "recipient": "+966501234567",
    "message": "مرحباً من n8n!",
    "message_type": "text",
    "phone_number_id": "YOUR_PHONE_NUMBER_ID",
    "whatsapp_token": "YOUR_WHATSAPP_TOKEN",
    "message_id": 123
  }'
```

### 2. اختبار استقبال رسالة:

```bash
curl -X POST https://n8n-n8n.mlqc5o.easypanel.host/webhook-test/a284c4ba-3c01-4fe7-9bce-0b18c3da3439 \
  -H "Content-Type: application/json" \
  -d '{
    "action": "receive_message",
    "user_id": 1,
    "sender": "+966501234567",
    "message": "مرحباً!",
    "execution_id": "test_execution_123"
  }'
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ 404 - Webhook not found:**
   - تأكد من أن الـ workflow مفعل
   - تحقق من صحة رابط الـ webhook

2. **خطأ في WhatsApp API:**
   - تحقق من صحة Access Token
   - تأكد من صحة Phone Number ID
   - راجع حدود API في Facebook Console

3. **خطأ في تحديث حالة الرسالة:**
   - تأكد من أن النظام يمكن الوصول إليه من n8n
   - تحقق من صحة رابط API في النظام

### سجلات الأخطاء:

1. **في n8n:**
   - اذهب إلى Executions tab
   - راجع تفاصيل التنفيذ الفاشل

2. **في النظام:**
   - راجع ملف `logs/error.log`
   - تحقق من جدول `activity_logs`

## 📈 تحسين الأداء

### نصائح للأداء الأمثل:

1. **استخدم Queues:**
   - أضف Queue nodes لمعالجة الرسائل بكميات كبيرة

2. **إعداد Retry Logic:**
   - أضف إعادة المحاولة للطلبات الفاشلة

3. **Monitoring:**
   - راقب executions في n8n
   - استخدم webhooks للتنبيهات

## 🔐 الأمان

### إجراءات الأمان:

1. **استخدم HTTPS:**
   - تأكد من أن جميع الروابط تستخدم HTTPS

2. **Webhook Secrets:**
   - أضف مفتاح أمان للـ webhooks
   - تحقق من الـ signatures

3. **Rate Limiting:**
   - أضف حدود للطلبات لتجنب الإساءة

## 📞 الدعم

### للحصول على المساعدة:

1. **وثائق n8n:**
   - https://docs.n8n.io/

2. **وثائق WhatsApp Business API:**
   - https://developers.facebook.com/docs/whatsapp

3. **مجتمع n8n:**
   - https://community.n8n.io/

---

**ملاحظة:** تأكد من استبدال `your-domain.com` برابط موقعك الفعلي في جميع الإعدادات.
