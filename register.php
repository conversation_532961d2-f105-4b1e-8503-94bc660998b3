<?php
require_once 'config/database.php';
require_once 'classes/User.php';

$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);

    // التحقق من صحة البيانات
    $errors = $user->validate($_POST);

    // التحقق من عدم وجود البريد الإلكتروني مسبقاً
    if (empty($errors) && $user->emailExists($_POST['email'])) {
        $errors[] = 'البريد الإلكتروني مستخدم مسبقاً';
    }

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    if (empty($errors) && $user->usernameExists($_POST['username'])) {
        $errors[] = 'اسم المستخدم مستخدم مسبقاً';
    }

    // التحقق من تطابق كلمة المرور
    if (empty($errors) && $_POST['password'] !== $_POST['confirm_password']) {
        $errors[] = 'كلمة المرور غير متطابقة';
    }

    if (empty($errors)) {
        // إنشاء المستخدم
        $user->username = $_POST['username'];
        $user->email = $_POST['email'];
        $user->password_hash = Helper::hashPassword($_POST['password']);
        $user->full_name = $_POST['full_name'];
        $user->phone = $_POST['phone'];
        $user->whatsapp_number = $_POST['whatsapp_number'];

        if ($user->register()) {
            $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.';
            
            // تسجيل الدخول التلقائي
            $_SESSION['user_id'] = $user->id;
            $_SESSION['username'] = $user->username;
            $_SESSION['full_name'] = $user->full_name;
            
            // إعادة توجيه إلى صفحة الاشتراكات
            header('Location: subscription.php');
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - إنشاء حساب جديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            margin: 20px auto;
        }
        .register-form {
            padding: 40px;
        }
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .register-header h2 {
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .register-header p {
            color: #666;
            margin-bottom: 0;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: translateY(-2px);
        }
        .register-side {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        .register-side h3 {
            font-weight: bold;
            margin-bottom: 20px;
        }
        .register-side .feature-list {
            list-style: none;
            padding: 0;
            margin: 30px 0;
        }
        .register-side .feature-list li {
            margin: 15px 0;
            font-size: 16px;
        }
        .register-side .feature-list i {
            margin-left: 10px;
            color: #ffd700;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        .input-group .form-control {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        .input-group .form-control:focus {
            border-left: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="row g-0">
                <!-- Registration Form -->
                <div class="col-lg-7">
                    <div class="register-form">
                        <div class="register-header">
                            <h2>
                                <i class="fab fa-whatsapp text-success me-2"></i>
                                إنشاء حساب جديد
                            </h2>
                            <p>انضم إلينا وابدأ في إدارة شات بوت الواتساب الخاص بك</p>
                        </div>

                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-user me-1"></i>
                                        الاسم الكامل
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-at me-1"></i>
                                        اسم المستخدم
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>
                                        كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>
                                        تأكيد كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone me-1"></i>
                                        رقم الهاتف
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="whatsapp_number" class="form-label">
                                        <i class="fab fa-whatsapp me-1"></i>
                                        رقم الواتساب
                                    </label>
                                    <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                           value="<?php echo htmlspecialchars($_POST['whatsapp_number'] ?? ''); ?>">
                                </div>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>
                                    إنشاء الحساب
                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0">
                                    لديك حساب بالفعل؟ 
                                    <a href="login.php" class="text-decoration-none fw-bold">
                                        تسجيل الدخول
                                    </a>
                                </p>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Side Panel -->
                <div class="col-lg-5">
                    <div class="register-side">
                        <h3>لماذا تختارنا؟</h3>
                        <p>احصل على أفضل حلول إدارة شات بوت الواتساب</p>
                        
                        <ul class="feature-list">
                            <li>
                                <i class="fas fa-robot"></i>
                                شات بوت ذكي متقدم
                            </li>
                            <li>
                                <i class="fas fa-chart-line"></i>
                                تتبع دقيق للاستهلاك
                            </li>
                            <li>
                                <i class="fas fa-cogs"></i>
                                تكامل سهل مع n8n
                            </li>
                            <li>
                                <i class="fas fa-shield-alt"></i>
                                أمان وحماية عالية
                            </li>
                            <li>
                                <i class="fas fa-headset"></i>
                                دعم فني متميز
                            </li>
                            <li>
                                <i class="fas fa-mobile-alt"></i>
                                واجهة سهلة الاستخدام
                            </li>
                        </ul>

                        <div class="mt-4">
                            <a href="index.php" class="btn btn-outline-light">
                                <i class="fas fa-home me-2"></i>
                                العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        // نسخ رقم الهاتف إلى رقم الواتساب
        document.getElementById('phone').addEventListener('input', function() {
            const whatsappField = document.getElementById('whatsapp_number');
            if (!whatsappField.value) {
                whatsappField.value = this.value;
            }
        });
    </script>
</body>
</html>
