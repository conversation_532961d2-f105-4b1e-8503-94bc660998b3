<?php
require_once 'config/database.php';

/**
 * فئة إدارة الرسائل والتكامل مع n8n
 * Message Management and n8n Integration Class
 */
class MessageManager {
    private $conn;
    private $table_name = "message_usage";
    private $n8n_settings_table = "user_n8n_settings";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * تسجيل رسالة جديدة
     * Log new message
     */
    public function logMessage($user_id, $subscription_id, $message_type, $recipient_number, $message_content = '', $n8n_workflow_id = '', $n8n_execution_id = '', $status = 'pending', $cost = 0.0) {
        $query = "INSERT INTO " . $this->table_name . " 
                 (user_id, subscription_id, message_type, recipient_number, message_content, n8n_workflow_id, n8n_execution_id, status, cost) 
                 VALUES (:user_id, :subscription_id, :message_type, :recipient_number, :message_content, :n8n_workflow_id, :n8n_execution_id, :status, :cost)";

        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':subscription_id', $subscription_id);
        $stmt->bindParam(':message_type', $message_type);
        $stmt->bindParam(':recipient_number', $recipient_number);
        $stmt->bindParam(':message_content', $message_content);
        $stmt->bindParam(':n8n_workflow_id', $n8n_workflow_id);
        $stmt->bindParam(':n8n_execution_id', $n8n_execution_id);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':cost', $cost);

        if ($stmt->execute()) {
            $message_id = $this->conn->lastInsertId();
            
            // تحديث الإحصائيات الشهرية
            $this->updateMonthlyStats($user_id, $message_type, $cost);
            
            Helper::logActivity($user_id, 'message_logged', 
                              'Message logged: ' . $message_type . ' to ' . $recipient_number);
            
            return $message_id;
        }

        return false;
    }

    /**
     * تحديث حالة الرسالة
     * Update message status
     */
    public function updateMessageStatus($message_id, $status, $n8n_execution_id = null) {
        $query = "UPDATE " . $this->table_name . " SET status = :status";
        
        if ($n8n_execution_id) {
            $query .= ", n8n_execution_id = :n8n_execution_id";
        }
        
        $query .= " WHERE id = :message_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':message_id', $message_id);
        
        if ($n8n_execution_id) {
            $stmt->bindParam(':n8n_execution_id', $n8n_execution_id);
        }

        return $stmt->execute();
    }

    /**
     * تحديث الإحصائيات الشهرية
     * Update monthly statistics
     */
    private function updateMonthlyStats($user_id, $message_type, $cost) {
        $year = date('Y');
        $month = date('n');

        $query = "INSERT INTO monthly_usage_stats (user_id, year, month, messages_sent, messages_received, total_cost) 
                 VALUES (:user_id, :year, :month, :messages_sent, :messages_received, :total_cost)
                 ON DUPLICATE KEY UPDATE 
                 messages_sent = messages_sent + :messages_sent_update,
                 messages_received = messages_received + :messages_received_update,
                 total_cost = total_cost + :total_cost_update";

        $stmt = $this->conn->prepare($query);
        
        $messages_sent = ($message_type == 'sent') ? 1 : 0;
        $messages_received = ($message_type == 'received') ? 1 : 0;

        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':year', $year);
        $stmt->bindParam(':month', $month);
        $stmt->bindParam(':messages_sent', $messages_sent);
        $stmt->bindParam(':messages_received', $messages_received);
        $stmt->bindParam(':total_cost', $cost);
        $stmt->bindParam(':messages_sent_update', $messages_sent);
        $stmt->bindParam(':messages_received_update', $messages_received);
        $stmt->bindParam(':total_cost_update', $cost);

        return $stmt->execute();
    }

    /**
     * إرسال رسالة عبر n8n
     * Send message via n8n
     */
    public function sendMessage($user_id, $recipient_number, $message_content, $message_type = 'text') {
        // التحقق من إمكانية إرسال الرسالة
        $subscription = new Subscription($this->conn);
        $can_send = $subscription->canSendMessage($user_id);
        
        if (!$can_send['can_send']) {
            return [
                'success' => false,
                'error' => $can_send['reason']
            ];
        }

        // الحصول على إعدادات n8n للمستخدم
        $n8n_settings = $this->getUserN8nSettings($user_id);
        if (!$n8n_settings || !$n8n_settings['is_configured']) {
            return [
                'success' => false,
                'error' => 'إعدادات n8n غير مكتملة'
            ];
        }

        // الحصول على الاشتراك النشط
        $active_subscription = $subscription->getActiveSubscription($user_id);
        if (!$active_subscription) {
            return [
                'success' => false,
                'error' => 'لا يوجد اشتراك نشط'
            ];
        }

        // تسجيل الرسالة أولاً
        $message_id = $this->logMessage(
            $user_id,
            $active_subscription['id'],
            'sent',
            $recipient_number,
            $message_content,
            '',
            '',
            'pending',
            0.01 // تكلفة افتراضية
        );

        if (!$message_id) {
            return [
                'success' => false,
                'error' => 'فشل في تسجيل الرسالة'
            ];
        }

        // إرسال الرسالة عبر n8n webhook
        $webhook_data = [
            'user_id' => $user_id,
            'message_id' => $message_id,
            'recipient' => $recipient_number,
            'message' => $message_content,
            'type' => $message_type,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $response = $this->callN8nWebhook($n8n_settings['n8n_webhook_url'], $webhook_data);
        
        if ($response['success']) {
            $this->updateMessageStatus($message_id, 'success', $response['execution_id'] ?? '');
            return [
                'success' => true,
                'message_id' => $message_id,
                'execution_id' => $response['execution_id'] ?? ''
            ];
        } else {
            $this->updateMessageStatus($message_id, 'failed');
            return [
                'success' => false,
                'error' => $response['error'],
                'message_id' => $message_id
            ];
        }
    }

    /**
     * استدعاء webhook في n8n
     * Call n8n webhook
     */
    private function callN8nWebhook($webhook_url, $data) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $webhook_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'User-Agent: WhatsApp-Bot-Manager/1.0'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => 'cURL Error: ' . $error
            ];
        }

        if ($http_code >= 200 && $http_code < 300) {
            $response_data = json_decode($response, true);
            return [
                'success' => true,
                'data' => $response_data,
                'execution_id' => $response_data['executionId'] ?? ''
            ];
        } else {
            return [
                'success' => false,
                'error' => 'HTTP Error: ' . $http_code . ' - ' . $response
            ];
        }
    }

    /**
     * الحصول على إعدادات n8n للمستخدم
     * Get user's n8n settings
     */
    public function getUserN8nSettings($user_id) {
        $query = "SELECT * FROM " . $this->n8n_settings_table . " WHERE user_id = :user_id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        return $stmt->rowCount() > 0 ? $stmt->fetch() : null;
    }

    /**
     * تحديث إعدادات n8n للمستخدم
     * Update user's n8n settings
     */
    public function updateN8nSettings($user_id, $webhook_url, $api_key = '', $whatsapp_token = '', $phone_number_id = '', $webhook_secret = '') {
        $query = "INSERT INTO " . $this->n8n_settings_table . " 
                 (user_id, n8n_webhook_url, n8n_api_key, whatsapp_business_api_token, whatsapp_phone_number_id, webhook_secret, is_configured) 
                 VALUES (:user_id, :webhook_url, :api_key, :whatsapp_token, :phone_number_id, :webhook_secret, :is_configured)
                 ON DUPLICATE KEY UPDATE 
                 n8n_webhook_url = :webhook_url_update,
                 n8n_api_key = :api_key_update,
                 whatsapp_business_api_token = :whatsapp_token_update,
                 whatsapp_phone_number_id = :phone_number_id_update,
                 webhook_secret = :webhook_secret_update,
                 is_configured = :is_configured_update";

        $stmt = $this->conn->prepare($query);
        
        $is_configured = !empty($webhook_url) && !empty($whatsapp_token) && !empty($phone_number_id);

        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':webhook_url', $webhook_url);
        $stmt->bindParam(':api_key', $api_key);
        $stmt->bindParam(':whatsapp_token', $whatsapp_token);
        $stmt->bindParam(':phone_number_id', $phone_number_id);
        $stmt->bindParam(':webhook_secret', $webhook_secret);
        $stmt->bindParam(':is_configured', $is_configured, PDO::PARAM_BOOL);
        
        // للتحديث
        $stmt->bindParam(':webhook_url_update', $webhook_url);
        $stmt->bindParam(':api_key_update', $api_key);
        $stmt->bindParam(':whatsapp_token_update', $whatsapp_token);
        $stmt->bindParam(':phone_number_id_update', $phone_number_id);
        $stmt->bindParam(':webhook_secret_update', $webhook_secret);
        $stmt->bindParam(':is_configured_update', $is_configured, PDO::PARAM_BOOL);

        if ($stmt->execute()) {
            Helper::logActivity($user_id, 'n8n_settings_updated', 'n8n settings updated');
            return true;
        }

        return false;
    }

    /**
     * معالجة webhook واردة من n8n
     * Process incoming webhook from n8n
     */
    public function processIncomingWebhook($data) {
        // التحقق من صحة البيانات
        if (!isset($data['user_id']) || !isset($data['message_type'])) {
            return ['success' => false, 'error' => 'بيانات غير مكتملة'];
        }

        $user_id = $data['user_id'];
        $message_type = $data['message_type'];
        $sender_number = $data['sender'] ?? '';
        $message_content = $data['message'] ?? '';
        $execution_id = $data['execution_id'] ?? '';

        // الحصول على الاشتراك النشط
        $subscription = new Subscription($this->conn);
        $active_subscription = $subscription->getActiveSubscription($user_id);
        
        if (!$active_subscription) {
            return ['success' => false, 'error' => 'لا يوجد اشتراك نشط'];
        }

        // تسجيل الرسالة الواردة
        $message_id = $this->logMessage(
            $user_id,
            $active_subscription['id'],
            $message_type,
            $sender_number,
            $message_content,
            '',
            $execution_id,
            'success',
            0.0
        );

        return [
            'success' => true,
            'message_id' => $message_id
        ];
    }

    /**
     * الحصول على رسائل المستخدم
     * Get user messages
     */
    public function getUserMessages($user_id, $limit = 50, $offset = 0, $message_type = null) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE user_id = :user_id";
        
        if ($message_type) {
            $query .= " AND message_type = :message_type";
        }
        
        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        
        if ($message_type) {
            $stmt->bindParam(':message_type', $message_type);
        }

        $stmt->execute();
        return $stmt->fetchAll();
    }
}
?>
