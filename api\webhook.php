<?php
/**
 * API Webhook للتكامل مع n8n
 * n8n Integration Webhook API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config/database.php';
require_once '../classes/User.php';
require_once '../classes/Subscription.php';
require_once '../classes/MessageManager.php';

$database = new Database();
$db = $database->getConnection();

$response = ['success' => false, 'message' => '', 'data' => null];

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // قراءة البيانات
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('بيانات JSON غير صحيحة');
    }

    // التحقق من وجود البيانات المطلوبة
    if (!isset($data['action'])) {
        throw new Exception('نوع العملية غير محدد');
    }

    $action = $data['action'];
    $messageManager = new MessageManager($db);

    switch ($action) {
        case 'send_message':
            // إرسال رسالة
            if (!isset($data['user_id']) || !isset($data['recipient']) || !isset($data['message'])) {
                throw new Exception('بيانات الرسالة غير مكتملة');
            }

            $result = $messageManager->sendMessage(
                $data['user_id'],
                $data['recipient'],
                $data['message'],
                $data['message_type'] ?? 'text'
            );

            $response = $result;
            break;

        case 'receive_message':
            // استقبال رسالة من n8n
            if (!isset($data['user_id']) || !isset($data['sender']) || !isset($data['message'])) {
                throw new Exception('بيانات الرسالة الواردة غير مكتملة');
            }

            $webhook_data = [
                'user_id' => $data['user_id'],
                'message_type' => 'received',
                'sender' => $data['sender'],
                'message' => $data['message'],
                'execution_id' => $data['execution_id'] ?? ''
            ];

            $result = $messageManager->processIncomingWebhook($webhook_data);
            $response = $result;
            break;

        case 'update_message_status':
            // تحديث حالة الرسالة
            if (!isset($data['message_id']) || !isset($data['status'])) {
                throw new Exception('بيانات تحديث الحالة غير مكتملة');
            }

            $success = $messageManager->updateMessageStatus(
                $data['message_id'],
                $data['status'],
                $data['execution_id'] ?? null
            );

            $response = [
                'success' => $success,
                'message' => $success ? 'تم تحديث حالة الرسالة' : 'فشل في تحديث حالة الرسالة'
            ];
            break;

        case 'check_user_limits':
            // التحقق من حدود المستخدم
            if (!isset($data['user_id'])) {
                throw new Exception('معرف المستخدم غير محدد');
            }

            $subscription = new Subscription($db);
            $limits = $subscription->canSendMessage($data['user_id']);
            
            $response = [
                'success' => true,
                'data' => $limits
            ];
            break;

        case 'get_user_settings':
            // الحصول على إعدادات المستخدم
            if (!isset($data['user_id'])) {
                throw new Exception('معرف المستخدم غير محدد');
            }

            $settings = $messageManager->getUserN8nSettings($data['user_id']);
            
            $response = [
                'success' => true,
                'data' => $settings ? [
                    'webhook_url' => $settings['n8n_webhook_url'],
                    'is_configured' => $settings['is_configured'],
                    'phone_number_id' => $settings['whatsapp_phone_number_id']
                ] : null
            ];
            break;

        case 'get_usage_stats':
            // الحصول على إحصائيات الاستخدام
            if (!isset($data['user_id'])) {
                throw new Exception('معرف المستخدم غير محدد');
            }

            $subscription = new Subscription($db);
            $period = $data['period'] ?? 'current_month';
            $stats = $subscription->getUsageStats($data['user_id'], $period);
            
            $response = [
                'success' => true,
                'data' => $stats
            ];
            break;

        case 'validate_webhook':
            // التحقق من صحة webhook (للتحقق من الاتصال)
            $response = [
                'success' => true,
                'message' => 'Webhook is working correctly',
                'timestamp' => date('Y-m-d H:i:s'),
                'server_info' => [
                    'php_version' => PHP_VERSION,
                    'server_time' => date('Y-m-d H:i:s'),
                    'timezone' => date_default_timezone_get()
                ]
            ];
            break;

        default:
            throw new Exception('نوع العملية غير مدعوم: ' . $action);
    }

} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => $e->getCode()
    ];
    
    // تسجيل الخطأ
    error_log('Webhook API Error: ' . $e->getMessage());
    
    http_response_code(400);
}

// إرسال الاستجابة
echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
exit;
?>
