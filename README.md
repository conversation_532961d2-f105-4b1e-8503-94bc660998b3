# نظام إدارة شات بوت الواتساب مع n8n
## WhatsApp Chatbot Management System with n8n Integration

نظام شامل لإدارة شات بوت الواتساب باستخدام n8n مع واجهة ويب لإدارة الاشتراكات وتتبع الاستهلاك.

## المميزات الرئيسية

### 🤖 إدارة شات بوت ذكي
- تكامل كامل مع n8n لإنشاء وإدارة سير العمل
- دعم رسائل الواتساب النصية والوسائط
- معالجة الرسائل الواردة والصادرة
- تتبع حالة الرسائل في الوقت الفعلي

### 📊 نظام الاشتراكات
- خطط اشتراك متعددة مع حدود مختلفة
- تتبع الاستهلاك الشهري للرسائل
- إدارة تواريخ انتهاء الاشتراكات
- نظام دفع مرن (يمكن ربطه بـ Stripe, PayPal, إلخ)

### 📈 التقارير والإحصائيات
- إحصائيات مفصلة للرسائل المرسلة والمستقبلة
- تقارير الاستهلاك الشهري والأسبوعي
- تتبع التكاليف والعائد
- رسوم بيانية تفاعلية

### 🔧 إعدادات متقدمة
- ربط سهل مع n8n webhooks
- إعدادات WhatsApp Business API
- إدارة أرقام الهواتف المتعددة
- نظام أمان متقدم

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- cURL extension
- JSON extension
- PDO MySQL extension

### الخدمات الخارجية
- n8n (يفضل النسخة المستضافة ذاتياً)
- WhatsApp Business API
- خدمة دفع (اختيارية)

## التثبيت والإعداد

### 1. تحضير قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE whatsapp_bot_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u username -p whatsapp_bot_system < database.sql
```

### 2. إعداد ملفات الإعدادات
```php
// تحديث config/database.php
private $host = 'localhost';
private $db_name = 'whatsapp_bot_system';
private $username = 'your_db_username';
private $password = 'your_db_password';
```

### 3. إعداد الصلاحيات
```bash
# إعطاء صلاحيات الكتابة للمجلدات
chmod 755 uploads/
chmod 755 logs/
chmod 755 temp/
```

### 4. إعداد n8n

#### إنشاء Workflow في n8n:
1. أنشئ workflow جديد في n8n
2. أضف Webhook node كنقطة البداية
3. أضف HTTP Request node للتواصل مع WhatsApp API
4. أضف HTTP Request node لإرسال النتائج إلى النظام

#### مثال على Webhook URL:
```
http://your-domain.com/api/webhook.php
```

### 5. إعداد WhatsApp Business API
1. احصل على رمز الوصول من Facebook Developers
2. احصل على Phone Number ID
3. قم بتحديث الإعدادات في لوحة التحكم

## استخدام النظام

### للمستخدمين الجدد:
1. قم بإنشاء حساب جديد من صفحة التسجيل
2. اختر خطة اشتراك مناسبة
3. قم بإعداد n8n webhook في صفحة الإعدادات
4. ابدأ في إرسال واستقبال الرسائل

### للمطورين:

#### API Endpoints:

**إرسال رسالة:**
```json
POST /api/webhook.php
{
    "action": "send_message",
    "user_id": 1,
    "recipient": "+966501234567",
    "message": "مرحباً من شات بوت الواتساب!",
    "message_type": "text"
}
```

**استقبال رسالة:**
```json
POST /api/webhook.php
{
    "action": "receive_message",
    "user_id": 1,
    "sender": "+966501234567",
    "message": "مرحباً!",
    "execution_id": "n8n_execution_id"
}
```

**التحقق من حدود المستخدم:**
```json
POST /api/webhook.php
{
    "action": "check_user_limits",
    "user_id": 1
}
```

## هيكل المشروع

```
ai1/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── classes/
│   ├── User.php             # فئة إدارة المستخدمين
│   ├── Subscription.php     # فئة إدارة الاشتراكات
│   └── MessageManager.php   # فئة إدارة الرسائل
├── api/
│   └── webhook.php          # API للتكامل مع n8n
├── uploads/                 # ملفات المستخدمين
├── logs/                    # ملفات السجلات
├── temp/                    # ملفات مؤقتة
├── index.php               # الصفحة الرئيسية
├── login.php               # صفحة تسجيل الدخول
├── register.php            # صفحة التسجيل
├── dashboard.php           # لوحة التحكم
├── subscription.php        # صفحة الاشتراكات
├── logout.php              # تسجيل الخروج
├── database.sql            # هيكل قاعدة البيانات
└── README.md               # هذا الملف
```

## الأمان

### إجراءات الأمان المطبقة:
- تشفير كلمات المرور باستخدام password_hash()
- حماية من SQL Injection باستخدام Prepared Statements
- تنظيف المدخلات باستخدام htmlspecialchars()
- نظام جلسات آمن
- تسجيل جميع الأنشطة

### توصيات إضافية:
- استخدم HTTPS في الإنتاج
- قم بتحديث مفاتيح الأمان في config/database.php
- فعل firewall على الخادم
- قم بعمل نسخ احتياطية دورية

## استكشاف الأخطاء

### مشاكل شائعة:

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من صحة بيانات الاتصال في config/database.php
- تأكد من تشغيل خدمة MySQL

**مشاكل في n8n webhook:**
- تأكد من صحة URL في إعدادات n8n
- تحقق من logs/ للأخطاء
- تأكد من إعدادات CORS

**مشاكل في WhatsApp API:**
- تحقق من صحة Access Token
- تأكد من صحة Phone Number ID
- راجع حدود API في Facebook Developers

## الدعم والتطوير

### للحصول على الدعم:
- راجع ملفات السجلات في مجلد logs/
- تحقق من جدول activity_logs في قاعدة البيانات
- استخدم أدوات المطور في المتصفح

### للمساهمة في التطوير:
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التعليقات
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

## التحديثات المستقبلية

### المميزات المخطط لها:
- [ ] دعم الرسائل الصوتية والصور
- [ ] نظام قوالب الرسائل
- [ ] تكامل مع المزيد من خدمات الدفع
- [ ] تطبيق موبايل
- [ ] نظام التقارير المتقدم
- [ ] دعم متعدد اللغات
- [ ] API أكثر تفصيلاً

---

**تم تطوير هذا النظام لإدارة شات بوت الواتساب بكفاءة عالية مع تكامل كامل مع n8n.**
