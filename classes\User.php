<?php
require_once 'config/database.php';

/**
 * فئة إدارة المستخدمين
 * User Management Class
 */
class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $email;
    public $password_hash;
    public $full_name;
    public $phone;
    public $whatsapp_number;
    public $status;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * تسجيل مستخدم جديد
     * Register new user
     */
    public function register() {
        $query = "INSERT INTO " . $this->table_name . " 
                 (username, email, password_hash, full_name, phone, whatsapp_number) 
                 VALUES (:username, :email, :password_hash, :full_name, :phone, :whatsapp_number)";

        $stmt = $this->conn->prepare($query);

        // تنظيف البيانات
        $this->username = Helper::sanitizeInput($this->username);
        $this->email = Helper::sanitizeInput($this->email);
        $this->full_name = Helper::sanitizeInput($this->full_name);
        $this->phone = Helper::sanitizeInput($this->phone);
        $this->whatsapp_number = Helper::sanitizeInput($this->whatsapp_number);

        // ربط المعاملات
        $stmt->bindParam(':username', $this->username);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':password_hash', $this->password_hash);
        $stmt->bindParam(':full_name', $this->full_name);
        $stmt->bindParam(':phone', $this->phone);
        $stmt->bindParam(':whatsapp_number', $this->whatsapp_number);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            Helper::logActivity($this->id, 'user_registered', 'User registered successfully');
            return true;
        }

        return false;
    }

    /**
     * تسجيل الدخول
     * User login
     */
    public function login($email, $password) {
        $query = "SELECT id, username, email, password_hash, full_name, status 
                 FROM " . $this->table_name . " 
                 WHERE email = :email AND status = 'active' LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            
            if (Helper::verifyPassword($password, $row['password_hash'])) {
                $this->id = $row['id'];
                $this->username = $row['username'];
                $this->email = $row['email'];
                $this->full_name = $row['full_name'];
                $this->status = $row['status'];
                
                Helper::logActivity($this->id, 'user_login', 'User logged in successfully');
                return true;
            }
        }

        return false;
    }

    /**
     * التحقق من وجود البريد الإلكتروني
     * Check if email exists
     */
    public function emailExists($email) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * التحقق من وجود اسم المستخدم
     * Check if username exists
     */
    public function usernameExists($username) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * الحصول على بيانات المستخدم
     * Get user data
     */
    public function getUserById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            $this->id = $row['id'];
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->full_name = $row['full_name'];
            $this->phone = $row['phone'];
            $this->whatsapp_number = $row['whatsapp_number'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }

        return false;
    }

    /**
     * تحديث بيانات المستخدم
     * Update user data
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                 SET full_name = :full_name, phone = :phone, whatsapp_number = :whatsapp_number 
                 WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $this->full_name = Helper::sanitizeInput($this->full_name);
        $this->phone = Helper::sanitizeInput($this->phone);
        $this->whatsapp_number = Helper::sanitizeInput($this->whatsapp_number);

        $stmt->bindParam(':full_name', $this->full_name);
        $stmt->bindParam(':phone', $this->phone);
        $stmt->bindParam(':whatsapp_number', $this->whatsapp_number);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            Helper::logActivity($this->id, 'user_updated', 'User profile updated');
            return true;
        }

        return false;
    }

    /**
     * تغيير كلمة المرور
     * Change password
     */
    public function changePassword($new_password) {
        $query = "UPDATE " . $this->table_name . " SET password_hash = :password_hash WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        
        $password_hash = Helper::hashPassword($new_password);
        $stmt->bindParam(':password_hash', $password_hash);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            Helper::logActivity($this->id, 'password_changed', 'User password changed');
            return true;
        }

        return false;
    }

    /**
     * الحصول على الاشتراك النشط للمستخدم
     * Get user's active subscription
     */
    public function getActiveSubscription() {
        $query = "SELECT us.*, sp.name, sp.name_ar, sp.max_messages_per_month, sp.max_contacts 
                 FROM user_subscriptions us 
                 JOIN subscription_plans sp ON us.plan_id = sp.id 
                 WHERE us.user_id = :user_id 
                 AND us.status = 'active' 
                 AND us.end_date >= CURDATE() 
                 ORDER BY us.end_date DESC 
                 LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $this->id);
        $stmt->execute();

        return $stmt->rowCount() > 0 ? $stmt->fetch() : null;
    }

    /**
     * الحصول على استهلاك الشهر الحالي
     * Get current month usage
     */
    public function getCurrentMonthUsage() {
        $current_month = date('Y-m');
        
        $query = "SELECT 
                    COUNT(*) as total_messages,
                    SUM(CASE WHEN message_type = 'sent' THEN 1 ELSE 0 END) as sent_messages,
                    SUM(CASE WHEN message_type = 'received' THEN 1 ELSE 0 END) as received_messages,
                    SUM(cost) as total_cost
                 FROM message_usage 
                 WHERE user_id = :user_id 
                 AND DATE_FORMAT(created_at, '%Y-%m') = :current_month";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $this->id);
        $stmt->bindParam(':current_month', $current_month);
        $stmt->execute();

        return $stmt->fetch();
    }

    /**
     * التحقق من صحة البيانات
     * Validate user data
     */
    public function validate($data) {
        $errors = [];

        if (empty($data['username']) || strlen($data['username']) < 3) {
            $errors[] = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
        }

        if (!Helper::isValidEmail($data['email'])) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }

        if (empty($data['password']) || strlen($data['password']) < Config::PASSWORD_MIN_LENGTH) {
            $errors[] = 'كلمة المرور يجب أن تكون ' . Config::PASSWORD_MIN_LENGTH . ' أحرف على الأقل';
        }

        if (empty($data['full_name'])) {
            $errors[] = 'الاسم الكامل مطلوب';
        }

        if (!empty($data['phone']) && !Helper::isValidPhone($data['phone'])) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }

        return $errors;
    }
}
?>
