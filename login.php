<?php
require_once 'config/database.php';
require_once 'classes/User.php';

$error_message = '';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $database = new Database();
    $db = $database->getConnection();
    $user = new User($db);

    $email = Helper::sanitizeInput($_POST['email']);
    $password = $_POST['password'];

    if ($user->login($email, $password)) {
        $_SESSION['user_id'] = $user->id;
        $_SESSION['username'] = $user->username;
        $_SESSION['full_name'] = $user->full_name;
        $_SESSION['email'] = $user->email;
        
        // إعادة توجيه إلى الصفحة المطلوبة أو الرئيسية
        $redirect = $_GET['redirect'] ?? 'index.php';
        header('Location: ' . $redirect);
        exit;
    } else {
        $error_message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Config::SITE_NAME_AR; ?> - تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 20px auto;
        }
        .login-form {
            padding: 50px 40px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }
        .login-header h2 {
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #764ba2, #667eea);
            transform: translateY(-2px);
        }
        .login-side {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 50px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        .login-side h3 {
            font-weight: bold;
            margin-bottom: 20px;
        }
        .login-side .feature-list {
            list-style: none;
            padding: 0;
            margin: 30px 0;
        }
        .login-side .feature-list li {
            margin: 20px 0;
            font-size: 16px;
        }
        .login-side .feature-list i {
            margin-left: 10px;
            color: #ffd700;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .forgot-password {
            text-align: center;
            margin: 20px 0;
        }
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .forgot-password a:hover {
            text-decoration: underline;
        }
        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        .divider span {
            background: white;
            padding: 0 20px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Form -->
                <div class="col-lg-6">
                    <div class="login-form">
                        <div class="login-header">
                            <h2>
                                <i class="fab fa-whatsapp text-success me-2"></i>
                                تسجيل الدخول
                            </h2>
                            <p>مرحباً بك مرة أخرى! سجل دخولك لإدارة شات بوت الواتساب</p>
                        </div>

                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="mb-4">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                       placeholder="أدخل بريدك الإلكتروني" required>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    كلمة المرور
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="أدخل كلمة المرور" required>
                            </div>

                            <div class="mb-4 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    تذكرني
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    تسجيل الدخول
                                </button>
                            </div>
                        </form>

                        <div class="forgot-password">
                            <a href="forgot-password.php">
                                <i class="fas fa-key me-1"></i>
                                نسيت كلمة المرور؟
                            </a>
                        </div>

                        <div class="divider">
                            <span>أو</span>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">
                                ليس لديك حساب؟ 
                                <a href="register.php" class="text-decoration-none fw-bold" style="color: #667eea;">
                                    إنشاء حساب جديد
                                </a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Side Panel -->
                <div class="col-lg-6">
                    <div class="login-side">
                        <h3>مرحباً بعودتك!</h3>
                        <p>استمر في إدارة شات بوت الواتساب الخاص بك بكفاءة</p>
                        
                        <ul class="feature-list">
                            <li>
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة تحكم شاملة
                            </li>
                            <li>
                                <i class="fas fa-chart-bar"></i>
                                تقارير مفصلة
                            </li>
                            <li>
                                <i class="fas fa-comments"></i>
                                إدارة الرسائل
                            </li>
                            <li>
                                <i class="fas fa-cog"></i>
                                إعدادات متقدمة
                            </li>
                            <li>
                                <i class="fas fa-mobile-alt"></i>
                                واجهة متجاوبة
                            </li>
                        </ul>

                        <div class="mt-4">
                            <a href="index.php" class="btn btn-outline-light">
                                <i class="fas fa-home me-2"></i>
                                العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
